import { createRouter, createWebHistory } from 'vue-router'
import LoginView from '../views/LoginView.vue'
import MoodView from '../views/MoodView.vue'
import BookView from '../views/BookView.vue'
import FoodView from '../views/FoodView.vue'

const routes = [
  { path: '/', redirect: '/login' },
  { path: '/login', component: LoginView },
  { path: '/moods', component: MoodView },
  { path: '/books', component: BookView },
  { path: '/food-items', component: FoodView },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

export default router
