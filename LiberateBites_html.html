<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Online Bakery and Confectionery Delivery at Home | Liberate Bites</title>
    <meta name="description" content="Fresh baked goods delivered to your door. Order custom cakes, artisan breads, and delicious pastries from Liberate Bites - your local online bakery.">
    <meta name="keywords" content="bakery, online bakery, cake delivery, fresh bread, pastries, custom cakes, confectionery">
    <meta name="author" content="Liberate Bites">
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: #ffffff;
            color: #333333;
            line-height: 1.6;
            scroll-behavior: smooth;
            font-weight: 400;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header - Flurys Style */
        .header {
            background: #ffffff;
            border-bottom: 1px solid #f0f0f0;
            padding: 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .header-top {
            background: #fbc900;
            color: #000000;
            padding: 10px 0;
            font-size: 13px;
            text-align: center;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .header-main {
            padding: 20px 0;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .logo img {
            height: 50px;
            width: auto;
            transition: transform 0.3s ease;
        }

        .logo:hover img {
            transform: scale(1.05);
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 40px;
            align-items: center;
            margin: 0;
            padding: 0;
        }

        .nav-menu li a {
            color: #333333;
            text-decoration: none;
            font-weight: 400;
            font-size: 15px;
            transition: all 0.3s ease;
            position: relative;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 8px 0;
        }

        .nav-menu li a:hover {
            color: #fa4f1f;
        }

        .nav-menu li a::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 1px;
            background: #fa4f1f;
            transition: width 0.3s ease;
        }

        .nav-menu li a:hover::after {
            width: 100%;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .search-box {
            display: flex;
            align-items: center;
            background: #ffffff;
            border-radius: 20px;
            padding: 8px 16px;
            border: 1px solid #e8e8e8;
            transition: all 0.3s ease;
        }

        .search-box:focus-within {
            border-color: #fa4f1f;
            box-shadow: 0 0 0 2px rgba(250, 79, 31, 0.1);
        }

        .search-box input {
            border: none;
            background: none;
            outline: none;
            padding: 6px 8px;
            font-size: 13px;
            width: 180px;
            color: #333333;
            font-family: inherit;
        }

        .search-box input::placeholder {
            color: #999;
        }

        .search-box button {
            border: none;
            background: none;
            color: #333333;
            cursor: pointer;
            padding: 4px;
            transition: color 0.3s ease;
        }

        .search-box button:hover {
            color: #fa4f1f;
        }

        .cart-icon, .user-icon {
            position: relative;
            cursor: pointer;
            font-size: 18px;
            color: #333333;
            transition: color 0.3s ease;
        }

        .cart-icon:hover, .user-icon:hover {
            color: #fa4f1f;
        }

        .cart-count {
            position: absolute;
            top: -6px;
            right: -6px;
            background: #fa4f1f;
            color: #ffffff;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 11px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            border: 2px solid #ffffff;
        }

        /* Hero Banner - Flurys Style */
        .hero-banner {
            background: linear-gradient(135deg, #fbc900 0%, #fa4f1f 100%);
            color: #000000;
            padding: 80px 0;
            position: relative;
            overflow: hidden;
        }

        .hero-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('https://images.unsplash.com/photo-1486427944299-d1955d23e34d?w=1200&h=600&fit=crop') center/cover;
            opacity: 0.1;
        }

        .hero-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .hero-text {
            animation: slideInLeft 1s ease-out;
        }

        .hero-title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 18px;
            margin-bottom: 40px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .hero-buttons {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .hero-btn {
            padding: 15px 30px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .hero-btn.primary {
            background: #fa4f1f;
            color: #ffffff;
            border: 2px solid #fa4f1f;
        }

        .hero-btn.primary:hover {
            background: #e63e0f;
            border-color: #e63e0f;
            transform: translateY(-2px);
        }

        .hero-btn.secondary {
            background: transparent;
            color: #000000;
            border: 2px solid #000000;
        }

        .hero-btn.secondary:hover {
            background: #000000;
            color: #ffffff;
            transform: translateY(-2px);
        }

        .hero-image {
            position: relative;
            animation: slideInRight 1s ease-out;
        }

        .hero-img {
            width: 100%;
            height: 400px;
            object-fit: cover;
            border-radius: 10px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        /* Below Banner Stats Section - Exact match to Flurys */
        .stats-section {
            background: #ffffff;
            padding: 60px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .stats-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 40px;
            text-align: center;
        }

        .stat-item {
            padding: 30px 20px;
            transition: transform 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #fbc900 0%, #fa4f1f 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 24px;
            color: #000000;
        }

        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #fa4f1f;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 16px;
            color: #666666;
            font-weight: 500;
        }

        /* Featured Products Section */
        .featured-section {
            padding: 80px 0;
            background: #f8f8f8;
        }

        .section-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .section-title {
            font-size: 36px;
            background: linear-gradient(135deg, #fbc900 0%, #fa4f1f 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .section-subtitle {
            font-size: 18px;
            color: #666666;
            max-width: 600px;
            margin: 0 auto;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .product-card {
            background: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            opacity: 0;
            transform: translateY(30px);
            animation: cardSlideUp 0.8s ease-out forwards;
        }

        .product-card:nth-child(1) { animation-delay: 0.1s; }
        .product-card:nth-child(2) { animation-delay: 0.2s; }
        .product-card:nth-child(3) { animation-delay: 0.3s; }
        .product-card:nth-child(4) { animation-delay: 0.4s; }

        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .product-image {
            width: 100%;
            height: 250px;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .product-card:hover .product-image {
            transform: scale(1.05);
        }

        .product-info {
            padding: 25px;
        }

        .product-name {
            font-size: 20px;
            font-weight: 600;
            color: #333333;
            margin-bottom: 10px;
        }

        .product-description {
            color: #666666;
            font-size: 14px;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .product-price {
            font-size: 18px;
            font-weight: 600;
            color: #fa4f1f;
            margin-bottom: 15px;
        }

        .buy-button {
            background: linear-gradient(135deg, #fbc900 0%, #fa4f1f 100%);
            color: #000000;
            padding: 12px 24px;
            border: 2px solid #fbc900;
            border-radius: 25px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-family: inherit;
        }

        .buy-button:hover {
            background: linear-gradient(135deg, #e6b500 0%, #e63e0f 100%);
            border-color: #fa4f1f;
            transform: translateY(-2px);
        }

        /* Animations */
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes cardSlideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Accessibility improvements */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus styles for better accessibility */
        a:focus, button:focus, input:focus {
            outline: 2px solid #fa4f1f;
            outline-offset: 2px;
        }

        /* Improved button hover effects */
        .hero-btn:focus, .buy-button:focus {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* Loading animation */
        .loading {
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
            }

            .nav-menu {
                gap: 15px;
                flex-wrap: wrap;
                justify-content: center;
                font-size: 14px;
            }

            .search-box input {
                width: 150px;
            }

            .hero-content {
                grid-template-columns: 1fr;
                gap: 40px;
                text-align: center;
                padding: 40px 20px;
            }

            .hero-title {
                font-size: 36px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 30px;
            }

            .section-title {
                font-size: 28px;
            }

            .product-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .header-top {
                font-size: 12px;
                padding: 6px 0;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .hero-title {
                font-size: 28px;
            }

            .hero-subtitle {
                font-size: 16px;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
                gap: 15px;
            }

            .hero-btn {
                width: 100%;
                max-width: 250px;
                padding: 12px 20px;
            }

            .nav-menu {
                gap: 10px;
                font-size: 13px;
            }

            .search-box {
                padding: 6px 15px;
            }

            .search-box input {
                width: 120px;
                font-size: 12px;
            }

            .section-title {
                font-size: 24px;
            }

            .section-subtitle {
                font-size: 16px;
            }
        }

        /* Print styles */
        @media print {
            .header-actions, .hero-buttons, .buy-button, .scroll-to-top {
                display: none;
            }

            body {
                font-size: 12pt;
                line-height: 1.4;
            }
        }
    </style>
</head>
<body>
    <!-- Header - Exact Flurys Style -->
    <header class="header">
        <div class="header-top">
            WE DELIVER PAN INDIA IN 48 - 72 HOURS. ORDER NOW
        </div>
        
        <div class="header-main">
            <div class="header-content">
                <a href="#" class="logo">
                    <?xml version="1.0" encoding="UTF-8"?>
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="226" height="175">
<path d="M0 0 C74.58 0 149.16 0 226 0 C226 57.75 226 115.5 226 175 C151.42 175 76.84 175 0 175 C0 117.25 0 59.5 0 0 Z " fill="#FCFCFB" transform="translate(0,0)"/>
<path d="M0 0 C1.27810547 -0.04253906 1.27810547 -0.04253906 2.58203125 -0.0859375 C6.12526124 0.54090079 7.63667198 1.85311168 10.1875 4.375 C12.171875 7.46875 12.171875 7.46875 13.9375 10.875 C14.53046875 11.9990625 15.1234375 13.123125 15.734375 14.28125 C17.1875 17.375 17.1875 17.375 17.703125 19.59375 C18.06006989 21.48884619 18.06006989 21.48884619 19.64453125 22.83203125 C21.57848232 24.76598232 22.08346584 26.46174491 22.875 29.0625 C23.12636719 29.87074219 23.37773438 30.67898437 23.63671875 31.51171875 C23.81847656 32.12660156 24.00023438 32.74148437 24.1875 33.375 C24.8475 33.375 25.5075 33.375 26.1875 33.375 C26.6825 35.85 26.6825 35.85 27.1875 38.375 C27.8475 38.375 28.5075 38.375 29.1875 38.375 C29.1875 39.035 29.1875 39.695 29.1875 40.375 C30.1775 40.375 31.1675 40.375 32.1875 40.375 C32.55875 39.446875 32.55875 39.446875 32.9375 38.5 C34.4531651 35.92336933 35.44263603 35.47582986 38.1875 34.375 C43.14153223 33.60974299 46.96353207 33.60065295 51.25 36.3125 C53.87399323 40.4601022 53.96903366 43.49591834 54.1875 48.375 C48.9075 48.375 43.6275 48.375 38.1875 48.375 C38.1875 49.035 38.1875 49.695 38.1875 50.375 C41.4875 50.375 44.7875 50.375 48.1875 50.375 C49.1775 52.85 49.1775 52.85 50.1875 55.375 C48.8675 55.705 47.5475 56.035 46.1875 56.375 C46.1875 57.035 46.1875 57.695 46.1875 58.375 C44.8675 58.375 43.5475 58.375 42.1875 58.375 C42.6825 63.325 42.6825 63.325 43.1875 68.375 C43.8475 68.375 44.5075 68.375 45.1875 68.375 C45.166875 67.570625 45.14625 66.76625 45.125 65.9375 C45.1875 63.375 45.1875 63.375 46.1875 62.375 C50.52280918 62.23106543 54.84907731 62.33287939 59.1875 62.375 C59.1875 66.995 59.1875 71.615 59.1875 76.375 C62.1575 76.375 65.1275 76.375 68.1875 76.375 C68.5175 78.355 68.8475 80.335 69.1875 82.375 C69.88875 81.776875 70.59 81.17875 71.3125 80.5625 C77.46970418 75.87767074 82.62489141 74.80843014 90.1875 74.375 C89.8575 71.405 89.5275 68.435 89.1875 65.375 C87.88554687 65.39820312 86.58359375 65.42140625 85.2421875 65.4453125 C83.51563076 65.46407942 81.78906779 65.48227863 80.0625 65.5 C79.2065625 65.51675781 78.350625 65.53351563 77.46875 65.55078125 C72.62162707 65.58806681 68.74042095 65.27080643 64.1875 63.375 C64.1875 62.715 64.1875 62.055 64.1875 61.375 C63.011875 60.941875 63.011875 60.941875 61.8125 60.5 C59.1875 59.375 59.1875 59.375 57.1875 57.375 C56.96044922 55.12841797 56.96044922 55.12841797 56.9921875 52.3671875 C56.99863281 51.38105469 57.00507813 50.39492188 57.01171875 49.37890625 C57.03685547 47.83009766 57.03685547 47.83009766 57.0625 46.25 C57.07152344 45.20972656 57.08054687 44.16945312 57.08984375 43.09765625 C57.11346133 40.52334003 57.14933316 37.94914162 57.1875 35.375 C61.8075 35.375 66.4275 35.375 71.1875 35.375 C71.1875 37.025 71.1875 38.675 71.1875 40.375 C70.1875 41.375 70.1875 41.375 67.125 41.4375 C66.155625 41.416875 65.18625 41.39625 64.1875 41.375 C64.21070313 42.43460938 64.23390625 43.49421875 64.2578125 44.5859375 C64.27656944 45.9739513 64.29476972 47.3619727 64.3125 48.75 C64.33763672 49.79800781 64.33763672 49.79800781 64.36328125 50.8671875 C64.38094267 52.70397486 64.29053261 54.54101963 64.1875 56.375 C63.1975 57.365 63.1975 57.365 62.1875 58.375 C67.1757251 59.99488753 71.38769296 60.64690003 76.6875 60.625 C78.55921875 60.63273437 78.55921875 60.63273437 80.46875 60.640625 C84.38454225 60.36092555 87.4727333 59.58134176 91.1875 58.375 C90.8575 57.715 90.5275 57.055 90.1875 56.375 C89.51203125 56.47039062 88.8365625 56.56578125 88.140625 56.6640625 C87.24859375 56.77492187 86.3565625 56.88578125 85.4375 57 C84.55578125 57.11601563 83.6740625 57.23203125 82.765625 57.3515625 C79.91283645 57.37749694 78.56341691 56.90684116 76.1875 55.375 C72.82877288 51.53645472 72.77657894 47.75043828 72.59375 42.875 C73.50253372 39.04854224 75.0174227 37.66833377 78.1875 35.375 C81.32677007 34.78688913 84.3207323 34.90065783 87.5 35.0625 C91.8003125 35.2171875 91.8003125 35.2171875 96.1875 35.375 C96.1875 42.635 96.1875 49.895 96.1875 57.375 C95.5275 57.375 94.8675 57.375 94.1875 57.375 C93.8575 58.695 93.5275 60.015 93.1875 61.375 C93.785625 61.6225 94.38375 61.87 95 62.125 C97.60070743 63.61111853 98.73166577 64.75449839 100.1875 67.375 C100.60779088 71.40979241 100.18390078 73.88129864 98.1875 77.375 C98.1875 78.035 98.1875 78.695 98.1875 79.375 C98.97125 79.601875 99.755 79.82875 100.5625 80.0625 C103.1875 81.375 103.1875 81.375 104.5 84 C104.726875 84.78375 104.95375 85.5675 105.1875 86.375 C107.42158258 85.49100092 107.42158258 85.49100092 108.5625 82.9375 C110.30016097 80.19734232 111.30212718 79.62532822 114.1875 78.375 C114.5175 77.715 114.8475 77.055 115.1875 76.375 C122.24911951 74.02112683 130.27264695 74.86050798 137.1875 77.375 C137.1875 78.035 137.1875 78.695 137.1875 79.375 C138.5075 79.705 139.8275 80.035 141.1875 80.375 C140.57239368 84.68074422 139.59682954 86.35945076 137.1875 90.375 C131.9075 89.715 126.6275 89.055 121.1875 88.375 C121.5175 89.695 121.8475 91.015 122.1875 92.375 C127.1375 93.365 132.0875 94.355 137.1875 95.375 C137.5175 96.365 137.8475 97.355 138.1875 98.375 C139.1775 98.705 140.1675 99.035 141.1875 99.375 C143.15342533 103.30685066 142.65888856 108.08908555 142.1875 112.375 C140.25 115.375 140.25 115.375 138.1875 117.375 C137.8575 118.365 137.5275 119.355 137.1875 120.375 C134.42184618 121.29688461 132.80466298 121.49285222 129.95703125 121.47265625 C129.15458984 121.46943359 128.35214844 121.46621094 127.52539062 121.46289062 C126.69201172 121.45451172 125.85863281 121.44613281 125 121.4375 C124.15501953 121.43298828 123.31003906 121.42847656 122.43945312 121.42382812 C120.35543845 121.41205403 118.27146023 121.3941189 116.1875 121.375 C116.1875 120.715 116.1875 120.055 116.1875 119.375 C113.2175 118.715 110.2475 118.055 107.1875 117.375 C107.69785831 112.9859185 108.70964277 110.03791938 111.1875 106.375 C112.0228125 106.5915625 112.0228125 106.5915625 112.875 106.8125 C118.12886317 108.09046672 122.78458529 108.57146963 128.1875 108.375 C128.1875 107.715 128.1875 107.055 128.1875 106.375 C126.88425781 106.01148437 126.88425781 106.01148437 125.5546875 105.640625 C114.10841388 102.31173111 114.10841388 102.31173111 109.1875 98.375 C109.1875 100.025 109.1875 101.675 109.1875 103.375 C99.2875 103.375 89.3875 103.375 79.1875 103.375 C80.6875 107.375 80.6875 107.375 83.1875 108.375 C84.60151385 108.47115294 86.02022892 108.50482065 87.4375 108.5 C88.24316406 108.5 89.04882812 108.5 89.87890625 108.5 C92.366014 108.4472179 92.366014 108.4472179 95.375 107.5 C98.1875 107.375 98.1875 107.375 99.921875 108.50390625 C101.63971568 110.07596687 103.02038456 111.35411919 104.1875 113.375 C103.78961329 116.66827768 102.76135484 118.29611725 100.1875 120.375 C96.29179113 121.67356962 92.56219608 121.63824136 88.5 121.6875 C87.33307617 121.73100586 87.33307617 121.73100586 86.14257812 121.77539062 C80.40505396 121.83759898 75.94432789 120.68957784 71.1875 117.375 C70.8575 116.715 70.5275 116.055 70.1875 115.375 C69.919375 115.99375 69.65125 116.6125 69.375 117.25 C68.1875 119.375 68.1875 119.375 65.1875 121.375 C62.69644165 121.60205078 62.69644165 121.60205078 59.69384766 121.5703125 C58.56902985 121.56370605 57.44421204 121.55709961 56.28530884 121.55029297 C55.06967255 121.53369629 53.85403625 121.51709961 52.6015625 121.5 C51.35249115 121.49242676 50.1034198 121.48485352 48.8164978 121.47705078 C44.13717399 121.44841923 39.45815052 121.40199886 34.7789917 121.35424805 C30.19904951 121.30844173 25.61915727 121.27772709 21.0390625 121.25 C19.82747467 121.23340332 18.61588684 121.21680664 17.36758423 121.19970703 C16.2364621 121.19310059 15.10533997 121.18649414 13.93994141 121.1796875 C12.9474942 121.16921387 11.955047 121.15874023 10.93252563 121.14794922 C7.99510569 121.39091361 5.89787102 122.26811952 3.1875 123.375 C-0.10666842 123.09583319 -2.84374226 122.85937887 -5.8125 121.375 C-7.53777121 121.25582832 -9.26742898 121.19782674 -10.99658203 121.16967773 C-12.07755157 121.14973251 -13.15852112 121.12978729 -14.27224731 121.10923767 C-15.44310883 121.09252518 -16.61397034 121.07581268 -17.8203125 121.05859375 C-19.6139473 121.0274221 -19.6139473 121.0274221 -21.44381714 120.99562073 C-25.27498464 120.93007711 -29.10622238 120.87121782 -32.9375 120.8125 C-35.52995527 120.76931937 -38.12240336 120.72570523 -40.71484375 120.68164062 C-47.08065427 120.57439081 -53.44653513 120.4726334 -59.8125 120.375 C-59.8125 119.715 -59.8125 119.055 -59.8125 118.375 C-60.45574219 118.13652344 -61.09898438 117.89804688 -61.76171875 117.65234375 C-63.8125 116.375 -63.8125 116.375 -64.45703125 114.31640625 C-64.54017578 113.13884766 -64.54017578 113.13884766 -64.625 111.9375 C-64.72748047 110.76380859 -64.72748047 110.76380859 -64.83203125 109.56640625 C-64.8125 107.375 -64.8125 107.375 -63.8125 104.375 C-63.1525 104.375 -62.4925 104.375 -61.8125 104.375 C-61.71839844 103.694375 -61.62429687 103.01375 -61.52734375 102.3125 C-60.65131014 98.71262417 -59.0841742 95.63322209 -57.375 92.375 C-55.33499523 88.43800111 -53.37867796 84.53048726 -51.8125 80.375 C-51.1525 80.375 -50.4925 80.375 -49.8125 80.375 C-49.74546875 79.59382813 -49.6784375 78.81265625 -49.609375 78.0078125 C-48.51546702 73.02087905 -46.32406081 68.80522068 -44.0625 64.25 C-40.38930045 56.71058454 -36.88392191 49.1828778 -33.8125 41.375 C-33.1525 41.375 -32.4925 41.375 -31.8125 41.375 C-31.585625 40.26125 -31.35875 39.1475 -31.125 38 C-30.05329232 33.69052756 -28.23136162 30.07109932 -26.1640625 26.16015625 C-24.65322285 23.04677859 -23.52565085 19.88765193 -22.375 16.625 C-20.2905487 11.11191852 -18.56610277 7.89266605 -13.8125 4.375 C-10.0625 3.125 -10.0625 3.125 -6.8125 2.375 C-4.33231247 -0.10518753 -3.42734049 0.05138442 0 0 Z " fill="#FCCA05" transform="translate(72.8125,11.625)"/>
<path d="M0 0 C4.62 0 9.24 0 14 0 C14 1.65 14 3.3 14 5 C13 6 13 6 9.9375 6.0625 C8.968125 6.041875 7.99875 6.02125 7 6 C7.02320313 7.05960938 7.04640625 8.11921875 7.0703125 9.2109375 C7.08906944 10.5989513 7.10726972 11.9869727 7.125 13.375 C7.14175781 14.07367188 7.15851563 14.77234375 7.17578125 15.4921875 C7.19344267 17.32897486 7.10303261 19.16601963 7 21 C6.34 21.66 5.68 22.32 5 23 C9.9882251 24.61988753 14.20019296 25.27190003 19.5 25.25 C21.37171875 25.25773437 21.37171875 25.25773437 23.28125 25.265625 C27.19704225 24.98592555 30.2852333 24.20634176 34 23 C33.505 22.01 33.505 22.01 33 21 C32.32453125 21.09539062 31.6490625 21.19078125 30.953125 21.2890625 C29.61507812 21.45535156 29.61507812 21.45535156 28.25 21.625 C26.92742187 21.79902344 26.92742187 21.79902344 25.578125 21.9765625 C22.72533645 22.00249694 21.37591691 21.53184116 19 20 C15.64127288 16.16145472 15.58907894 12.37543828 15.40625 7.5 C16.31503372 3.67354224 17.8299227 2.29333377 21 0 C24.13927007 -0.58811087 27.1332323 -0.47434217 30.3125 -0.3125 C34.6128125 -0.1578125 34.6128125 -0.1578125 39 0 C39 7.26 39 14.52 39 22 C38.34 22 37.68 22 37 22 C36.67 23.32 36.34 24.64 36 26 C36.598125 26.2475 37.19625 26.495 37.8125 26.75 C40.41320743 28.23611853 41.54416577 29.37949839 43 32 C43.42029088 36.03479241 42.99640078 38.50629864 41 42 C41 42.66 41 43.32 41 44 C42.175625 44.3403125 42.175625 44.3403125 43.375 44.6875 C46 46 46 46 47.3125 48.625 C47.539375 49.40875 47.76625 50.1925 48 51 C50.23408258 50.11600092 50.23408258 50.11600092 51.375 47.5625 C53.11266097 44.82234232 54.11462718 44.25032822 57 43 C57.495 42.01 57.495 42.01 58 41 C65.06161951 38.64612683 73.08514695 39.48550798 80 42 C80 42.66 80 43.32 80 44 C81.98 44.495 81.98 44.495 84 45 C83.38489368 49.30574422 82.40932954 50.98445076 80 55 C74.72 54.34 69.44 53.68 64 53 C64.495 54.98 64.495 54.98 65 57 C69.95 57.99 74.9 58.98 80 60 C80.33 60.99 80.66 61.98 81 63 C82.485 63.495 82.485 63.495 84 64 C85.96592533 67.93185066 85.47138856 72.71408555 85 77 C83.0625 80 83.0625 80 81 82 C80.505 83.485 80.505 83.485 80 85 C77.23434618 85.92188461 75.61716298 86.11785222 72.76953125 86.09765625 C71.96708984 86.09443359 71.16464844 86.09121094 70.33789062 86.08789062 C69.50451172 86.07951172 68.67113281 86.07113281 67.8125 86.0625 C66.96751953 86.05798828 66.12253906 86.05347656 65.25195312 86.04882812 C63.16793845 86.03705403 61.08396023 86.0191189 59 86 C59 85.34 59 84.68 59 84 C56.03 83.34 53.06 82.68 50 82 C50.51035831 77.6109185 51.52214277 74.66291938 54 71 C54.556875 71.144375 55.11375 71.28875 55.6875 71.4375 C60.94136317 72.71546672 65.59708529 73.19646963 71 73 C71 72.34 71 71.68 71 71 C70.13117188 70.75765625 69.26234375 70.5153125 68.3671875 70.265625 C56.92091388 66.93673111 56.92091388 66.93673111 52 63 C52 64.65 52 66.3 52 68 C42.1 68 32.2 68 22 68 C23.5 72 23.5 72 26 73 C27.41401385 73.09615294 28.83272892 73.12982065 30.25 73.125 C31.05566406 73.125 31.86132812 73.125 32.69140625 73.125 C35.178514 73.0722179 35.178514 73.0722179 38.1875 72.125 C41 72 41 72 42.734375 73.12890625 C44.45221568 74.70096687 45.83288456 75.97911919 47 78 C46.60211329 81.29327768 45.57385484 82.92111725 43 85 C39.10429113 86.29856962 35.37469608 86.26324136 31.3125 86.3125 C30.53455078 86.34150391 29.75660156 86.37050781 28.95507812 86.40039062 C23.21755396 86.46259898 18.75682789 85.31457784 14 82 C13.67 81.34 13.34 80.68 13 80 C12.731875 80.61875 12.46375 81.2375 12.1875 81.875 C11 84 11 84 8 86 C5.17108991 86.17187472 2.44038709 86.23223013 -0.38671875 86.1953125 C-1.11105103 86.19329834 -1.8353833 86.19128418 -2.58166504 86.18920898 C-5.28367458 86.17875634 -7.98559374 86.15083369 -10.6875 86.125 C-16.730625 86.08375 -22.77375 86.0425 -29 86 C-29 71.15 -29 56.3 -29 41 C-23.06 41 -17.12 41 -11 41 C-11 36.38 -11 31.76 -11 27 C-6.71 27 -2.42 27 2 27 C2 31.62 2 36.24 2 41 C4.97 41 7.94 41 11 41 C11.495 43.97 11.495 43.97 12 47 C12.70125 46.401875 13.4025 45.80375 14.125 45.1875 C20.28220418 40.50267074 25.43739141 39.43343014 33 39 C32.505 34.545 32.505 34.545 32 30 C30.69804687 30.02320312 29.39609375 30.04640625 28.0546875 30.0703125 C26.32813076 30.08907942 24.60156779 30.10727863 22.875 30.125 C22.0190625 30.14175781 21.163125 30.15851563 20.28125 30.17578125 C15.43412707 30.21306681 11.55292095 29.89580643 7 28 C7 27.34 7 26.68 7 26 C6.21625 25.71125 5.4325 25.4225 4.625 25.125 C2 24 2 24 0 22 C-0.22705078 19.75341797 -0.22705078 19.75341797 -0.1953125 16.9921875 C-0.18886719 16.00605469 -0.18242187 15.01992188 -0.17578125 14.00390625 C-0.15902344 12.97136719 -0.14226562 11.93882813 -0.125 10.875 C-0.11597656 9.83472656 -0.10695312 8.79445313 -0.09765625 7.72265625 C-0.07403867 5.14834003 -0.03816684 2.57414162 0 0 Z " fill="#FCCC10" transform="translate(130,47)"/>
<path d="M0 0 C0.91072266 0.28552734 1.82144531 0.57105469 2.75976562 0.86523438 C3.67822266 1.16365234 4.59667969 1.46207031 5.54296875 1.76953125 C15.50640077 4.94404638 25.53559886 7.83881484 35.59475708 10.69143677 C41.40905714 12.34279509 47.21985169 14.00643372 53.03125 15.66796875 C55.51495971 16.37765404 57.99868376 17.08728913 60.48242188 17.796875 C67.19823313 19.71567822 67.19823313 19.71567822 73.91404438 21.63448143 C74.55278083 21.81697756 75.19151728 21.99947369 75.84960938 22.1875 C84.2977066 24.60124207 84.2977066 24.60124207 92.74580383 27.01498413 C93.40074326 27.20210968 94.05568268 27.38923523 94.73046875 27.58203125 C94.73046875 27.91203125 94.73046875 28.24203125 94.73046875 28.58203125 C23.78046875 28.58203125 -47.16953125 28.58203125 -120.26953125 28.58203125 C-120.26953125 28.25203125 -120.26953125 27.92203125 -120.26953125 27.58203125 C-119.61807129 27.411875 -118.96661133 27.24171875 -118.29541016 27.06640625 C-115.16027293 26.24393174 -112.02744866 25.41291979 -108.89453125 24.58203125 C-107.87230469 24.31519531 -106.85007812 24.04835937 -105.796875 23.7734375 C-97.82674407 21.65121329 -89.95162787 19.2771486 -82.07421875 16.8359375 C-69.48926023 12.93800762 -56.87529563 9.15544224 -44.21337891 5.51489258 C-41.88673376 4.84243176 -39.56222274 4.16254938 -37.23974609 3.47583008 C-33.80649374 2.46147089 -30.3672185 1.47055568 -26.92578125 0.484375 C-25.89964722 0.17620346 -24.87351318 -0.13196808 -23.81628418 -0.44947815 C-15.45902465 -2.79949409 -8.2886918 -2.6632715 0 0 Z " fill="#FA4F1F" transform="translate(126.26953125,146.41796875)"/>
<path d="M0 0 C0.85207031 -0.02835938 1.70414063 -0.05671875 2.58203125 -0.0859375 C6.12526124 0.54090079 7.63667198 1.85311168 10.1875 4.375 C12.171875 7.46875 12.171875 7.46875 13.9375 10.875 C14.53046875 11.9990625 15.1234375 13.123125 15.734375 14.28125 C17.1875 17.375 17.1875 17.375 17.703125 19.59375 C18.06006989 21.48884619 18.06006989 21.48884619 19.64453125 22.83203125 C21.57848232 24.76598232 22.08346584 26.46174491 22.875 29.0625 C23.12636719 29.87074219 23.37773438 30.67898437 23.63671875 31.51171875 C23.81847656 32.12660156 24.00023438 32.74148437 24.1875 33.375 C24.8475 33.375 25.5075 33.375 26.1875 33.375 C26.6825 35.85 26.6825 35.85 27.1875 38.375 C27.8475 38.375 28.5075 38.375 29.1875 38.375 C29.1875 39.035 29.1875 39.695 29.1875 40.375 C30.1775 40.375 31.1675 40.375 32.1875 40.375 C32.55875 39.446875 32.55875 39.446875 32.9375 38.5 C34.4531651 35.92336933 35.44263603 35.47582986 38.1875 34.375 C43.14153223 33.60974299 46.96353207 33.60065295 51.25 36.3125 C53.87399323 40.4601022 53.96903366 43.49591834 54.1875 48.375 C48.9075 48.375 43.6275 48.375 38.1875 48.375 C38.1875 49.035 38.1875 49.695 38.1875 50.375 C41.4875 50.375 44.7875 50.375 48.1875 50.375 C49.1775 52.85 49.1775 52.85 50.1875 55.375 C47.05251708 57.23276765 44.81278568 57.57640476 41.1875 57.375 C41.3525 58.2 41.5175 59.025 41.6875 59.875 C42.24646045 63.78772317 42.25557187 67.42683173 42.1875 71.375 C36.03181924 70.44398067 30.57904257 69.58078207 25.1875 66.375 C26.63531793 62.93071735 28.46444609 61.01369038 31.3125 58.625 C32.03695312 58.01140625 32.76140625 57.3978125 33.5078125 56.765625 C34.06210938 56.30671875 34.61640625 55.8478125 35.1875 55.375 C33.5375 54.385 31.8875 53.395 30.1875 52.375 C29.836875 52.82875 29.48625 53.2825 29.125 53.75 C26.42323588 56.01599571 23.56351211 56.4997376 20.1875 57.375 C19.8575 57.705 19.5275 58.035 19.1875 58.375 C16.37392888 58.39197335 13.58799073 58.3458943 10.77734375 58.25390625 C9.93530899 58.23029526 9.09327423 58.20668427 8.22572327 58.18235779 C5.52531869 58.10553055 2.82518729 58.02158888 0.125 57.9375 C-1.70115832 57.88429836 -3.52733014 57.83155786 -5.35351562 57.77929688 C-9.84006775 57.65075991 -14.32618028 57.51261717 -18.8125 57.375 C-19.1425 47.145 -19.4725 36.915 -19.8125 26.375 C-20.8025 28.355 -21.7925 30.335 -22.8125 32.375 C-24.11158722 34.72986713 -25.46298463 37.04824936 -26.8125 39.375 C-27.4725 39.375 -28.1325 39.375 -28.8125 39.375 C-28.97685547 40.43654297 -28.97685547 40.43654297 -29.14453125 41.51953125 C-31.03570176 49.6040087 -36.12497509 56.9875574 -40.43359375 63.99609375 C-42.55290516 67.65235621 -44.15758253 71.4934663 -45.8125 75.375 C-48.26537609 80.49404576 -51.03174117 85.42898628 -53.8125 90.375 C-54.8125 88.375 -54.8125 88.375 -54.25390625 86.4296875 C-53.81884766 85.35074219 -53.81884766 85.35074219 -53.375 84.25 C-52.94380859 83.16332031 -52.94380859 83.16332031 -52.50390625 82.0546875 C-52.27574219 81.50039062 -52.04757813 80.94609375 -51.8125 80.375 C-51.1525 80.375 -50.4925 80.375 -49.8125 80.375 C-49.71195313 79.20324219 -49.71195313 79.20324219 -49.609375 78.0078125 C-48.51546702 73.02087905 -46.32406081 68.80522068 -44.0625 64.25 C-40.38930045 56.71058454 -36.88392191 49.1828778 -33.8125 41.375 C-33.1525 41.375 -32.4925 41.375 -31.8125 41.375 C-31.585625 40.26125 -31.35875 39.1475 -31.125 38 C-30.05329232 33.69052756 -28.23136162 30.07109932 -26.1640625 26.16015625 C-24.65322285 23.04677859 -23.52565085 19.88765193 -22.375 16.625 C-20.2905487 11.11191852 -18.56610277 7.89266605 -13.8125 4.375 C-10.0625 3.125 -10.0625 3.125 -6.8125 2.375 C-4.33231247 -0.10518753 -3.42734049 0.05138442 0 0 Z " fill="#B79523" transform="translate(72.8125,11.625)"/>
<path d="M0 0 C4.57875 -0.103125 9.1575 -0.20625 13.875 -0.3125 C16.0094458 -0.38078003 16.0094458 -0.38078003 18.18701172 -0.45043945 C25.85481798 -0.53351718 32.91411062 -0.16522001 39 5 C40.87135909 8.75191134 41.29650461 11.81987165 41.3125 16 C41.32925781 17.010625 41.34601563 18.02125 41.36328125 19.0625 C40.95136346 22.39327611 39.8255219 24.22559204 38 27 C38 27.66 38 28.32 38 29 C38.66 29.495 39.32 29.99 40 30.5 C40.66 30.995 41.32 31.49 42 32 C42 32.66 42 33.32 42 34 C42.66 34 43.32 34 44 34 C46.80328294 38.78800726 46.65458285 43.59588577 46 49 C44.93257543 51.70104825 43.69855316 53.61140962 42 56 C41.34 56 40.68 56 40 56 C39.67 56.99 39.34 57.98 39 59 C37.94039062 59.04898438 36.88078125 59.09796875 35.7890625 59.1484375 C34.40101503 59.22346709 33.01299549 59.29901485 31.625 59.375 C30.57699219 59.42140625 30.57699219 59.42140625 29.5078125 59.46875 C25.29814173 59.59499498 25.29814173 59.59499498 22 62 C17.72560565 61.88126682 15.71106354 59.99160934 12.625 57.2109375 C9.95673293 55.22256541 7.22296608 54.70905254 4 54 C4 54.66 4 55.32 4 56 C2.68 55.67 1.36 55.34 0 55 C0 36.85 0 18.7 0 0 Z " fill="#FEFCF6" transform="translate(55,70)"/>
<path d="M0 0 C4.29 0 8.58 0 13 0 C13 4.95 13 9.9 13 15 C15.31 15 17.62 15 20 15 C20.02687279 16.81242052 20.04633715 18.62495233 20.0625 20.4375 C20.07410156 21.44683594 20.08570313 22.45617187 20.09765625 23.49609375 C20 26 20 26 19 27 C17.02 27 15.04 27 13 27 C13.495 35.415 13.495 35.415 14 44 C15.65 44.33 17.3 44.66 19 45 C19.33 45.99 19.66 46.98 20 48 C20.515625 48.45375 21.03125 48.9075 21.5625 49.375 C23 51 23 51 23.4375 53.5 C23 56 23 56 21.50292969 57.63793945 C17.91379085 59.59110035 14.49488094 59.37142541 10.515625 59.29296875 C9.43063721 59.28843689 9.43063721 59.28843689 8.32373047 59.28381348 C5.63234098 59.26816586 2.94115555 59.22628174 0.25 59.1875 C-5.7725 59.125625 -11.795 59.06375 -18 59 C-18 44.15 -18 29.3 -18 14 C-12.06 14 -6.12 14 0 14 C0 9.38 0 4.76 0 0 Z " fill="#E22729" transform="translate(119,74)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 3.96 6 7.92 6 12 C6.66 12 7.32 12 8 12 C8 11.34 8 10.68 8 10 C13.27329606 9.68360224 16.51196218 10.17058485 21 13 C23.875 15.6875 23.875 15.6875 26 18 C26.3403125 16.8553125 26.3403125 16.8553125 26.6875 15.6875 C28.37823505 12.22551871 29.41024181 11.42094595 33 10 C38.03080069 9.24703477 41.69836857 9.17651889 46.0625 11.9375 C48.68649323 16.0851022 48.78153366 19.12091834 49 24 C43.72 24 38.44 24 33 24 C33 24.66 33 25.32 33 26 C36.3 26 39.6 26 43 26 C43.66 27.65 44.32 29.3 45 31 C41.86501708 32.85776765 39.62528568 33.20140476 36 33 C36.165 33.825 36.33 34.65 36.5 35.5 C37.05896045 39.41272317 37.06807187 43.05183173 37 47 C30.84431924 46.06898067 25.39154257 45.20578207 20 42 C21.44781793 38.55571735 23.27694609 36.63869038 26.125 34.25 C26.84945312 33.63640625 27.57390625 33.0228125 28.3203125 32.390625 C28.87460938 31.93171875 29.42890625 31.4728125 30 31 C28.35 30.01 26.7 29.02 25 28 C24.4740625 28.680625 24.4740625 28.680625 23.9375 29.375 C17.04577394 35.15515734 8.22347894 32.54823193 0 32 C0 21.44 0 10.88 0 0 Z " fill="#1F1E1E" transform="translate(78,36)"/>
<path d="M0 0 C2.31 0.99 4.62 1.98 7 3 C3.90597382 6.88963291 0.94135793 9.93449939 -3 13 C-3.33 13.66 -3.66 14.32 -4 15 C-3.0409375 15.1546875 -3.0409375 15.1546875 -2.0625 15.3125 C2.06617975 16.23934648 6.00314416 17.62280623 10 19 C11.485 19.495 11.485 19.495 13 20 C12.67 15.38 12.34 10.76 12 6 C13.65 6 15.3 6 17 6 C15.68 6.33 14.36 6.66 13 7 C13.495 11.95 13.495 11.95 14 17 C14.66 17 15.32 17 16 17 C16 15.35 16 13.7 16 12 C16.33 12 16.66 12 17 12 C17 16.29 17 20.58 17 25 C11.06 25 5.12 25 -1 25 C-1 31.6 -1 38.2 -1 45 C-1.99 43.02 -1.99 43.02 -3 41 C-3.66 41 -4.32 41 -5 41 C-6.70680947 39.37446717 -8.37660195 37.70884005 -10 36 C-9.5359375 35.1028125 -9.5359375 35.1028125 -9.0625 34.1875 C-7.20244691 30.35797893 -6.8387831 27.34080874 -6.8125 23.125 C-6.79832031 22.09632812 -6.78414063 21.06765625 -6.76953125 20.0078125 C-7.08588401 15.74186246 -7.08588401 15.74186246 -9 12 C-16.06396789 6.84480641 -24.69986771 7.34221243 -33.0625 7.25 C-39.9615625 7.12625 -39.9615625 7.12625 -47 7 C-46.67 25.15 -46.34 43.3 -46 62 C-45.01 62.33 -44.02 62.66 -43 63 C-43 62.34 -43 61.68 -43 61 C-38.19825184 61.19598972 -35.89737557 61.78067858 -32.375 65.125 C-29.76823209 67.72027661 -29.76823209 67.72027661 -26.125 67.9375 C-24.05807438 67.17858366 -24.05807438 67.17858366 -23 66 C-19.78980023 65.7637353 -16.58726128 65.60339698 -13.37109375 65.48242188 C-11.57586061 65.39075776 -9.78614053 65.20240144 -8 65 C-7.505 64.01 -7.505 64.01 -7 63 C-6.34 63 -5.68 63 -5 63 C-4.71125 62.21625 -4.4225 61.4325 -4.125 60.625 C-3 58 -3 58 -1 56 C-1 60.29 -1 64.58 -1 69 C-6.8860215 69.77193725 -12.48747657 70.05674428 -18.421875 69.83984375 C-21.30487041 70.01893892 -23.34089503 70.91906302 -26 72 C-29.29416842 71.72083319 -32.03124226 71.48437887 -35 70 C-36.72527121 69.88082832 -38.45492898 69.82282674 -40.18408203 69.79467773 C-41.26505157 69.77473251 -42.34602112 69.75478729 -43.45974731 69.73423767 C-44.63060883 69.71752518 -45.80147034 69.70081268 -47.0078125 69.68359375 C-48.8014473 69.6524221 -48.8014473 69.6524221 -50.63131714 69.62062073 C-54.46248464 69.55507711 -58.29372238 69.49621782 -62.125 69.4375 C-64.71745527 69.39431937 -67.30990336 69.35070523 -69.90234375 69.30664062 C-76.26815427 69.19939081 -82.63403513 69.0976334 -89 69 C-89 68.34 -89 67.68 -89 67 C-89.66 66.67 -90.32 66.34 -91 66 C-77.14 66.33 -63.28 66.66 -49 67 C-49.01160156 62.68035156 -49.02320313 58.36070313 -49.03515625 53.91015625 C-49.04158316 50.47483092 -49.04700739 47.03950729 -49.05175018 43.60417938 C-49.05703996 39.97284243 -49.06411357 36.34152865 -49.07543945 32.71020508 C-49.08627184 29.21540025 -49.09227758 25.72061796 -49.09487724 22.22579765 C-49.09763551 20.23760611 -49.10544688 18.24942369 -49.11329651 16.26124573 C-49.11337204 15.06007614 -49.11344757 13.85890656 -49.11352539 12.62133789 C-49.115746 11.55766495 -49.11796661 10.493992 -49.12025452 9.39808655 C-49 7 -49 7 -48 6 C-45.22339878 5.93610651 -42.47102258 5.93378761 -39.6953125 5.97265625 C-37.98047078 5.98530477 -36.26562692 5.99766557 -34.55078125 6.00976562 C-31.83603321 6.03458814 -29.12139483 6.06254436 -26.40673828 6.09594727 C-23.79458913 6.12612217 -21.18258021 6.14391898 -18.5703125 6.16015625 C-17.7577298 6.17361588 -16.94514709 6.1870755 -16.10794067 6.20094299 C-12.20380723 6.2183608 -10.32556054 6.21704036 -7 4 C-5.96875 3.67 -4.9375 3.34 -3.875 3 C-0.87488938 2.3313327 -0.87488938 2.3313327 0 0 Z " fill="#F3E08C" transform="translate(102,63)"/>
<path d="M0 0 C4.62 0 9.24 0 14 0 C14 1.65 14 3.3 14 5 C13 6 13 6 9.9375 6.0625 C8.968125 6.041875 7.99875 6.02125 7 6 C7.02320313 7.05960938 7.04640625 8.11921875 7.0703125 9.2109375 C7.08906944 10.5989513 7.10726972 11.9869727 7.125 13.375 C7.14175781 14.07367188 7.15851563 14.77234375 7.17578125 15.4921875 C7.19344267 17.32897486 7.10303261 19.16601963 7 21 C6.34 21.66 5.68 22.32 5 23 C9.9882251 24.61988753 14.20019296 25.27190003 19.5 25.25 C21.37171875 25.25773437 21.37171875 25.25773437 23.28125 25.265625 C27.19704225 24.98592555 30.2852333 24.20634176 34 23 C33.505 22.01 33.505 22.01 33 21 C32.32453125 21.09539062 31.6490625 21.19078125 30.953125 21.2890625 C30.06109375 21.39992188 29.1690625 21.51078125 28.25 21.625 C26.92742187 21.79902344 26.92742187 21.79902344 25.578125 21.9765625 C22.72533645 22.00249694 21.37591691 21.53184116 19 20 C15.64127288 16.16145472 15.58907894 12.37543828 15.40625 7.5 C16.31503372 3.67354224 17.8299227 2.29333377 21 0 C24.13927007 -0.58811087 27.1332323 -0.47434217 30.3125 -0.3125 C34.6128125 -0.1578125 34.6128125 -0.1578125 39 0 C39 7.26 39 14.52 39 22 C38.34 22 37.68 22 37 22 C36.67 23.32 36.34 24.64 36 26 C36.598125 26.2475 37.19625 26.495 37.8125 26.75 C40.41320743 28.23611853 41.54416577 29.37949839 43 32 C43.3125 35.125 43.3125 35.125 43 38 C41 40 41 40 38 40.1875 C35 40 35 40 33 39 C32.5859375 36.93359375 32.5859375 36.93359375 32.375 34.4375 C32.30023438 33.61121094 32.22546875 32.78492188 32.1484375 31.93359375 C32.09945312 31.29550781 32.05046875 30.65742187 32 30 C30.69804687 30.02320312 29.39609375 30.04640625 28.0546875 30.0703125 C26.32813076 30.08907942 24.60156779 30.10727863 22.875 30.125 C22.0190625 30.14175781 21.163125 30.15851563 20.28125 30.17578125 C15.43412707 30.21306681 11.55292095 29.89580643 7 28 C7 27.34 7 26.68 7 26 C5.824375 25.566875 5.824375 25.566875 4.625 25.125 C2 24 2 24 0 22 C-0.22705078 19.75341797 -0.22705078 19.75341797 -0.1953125 16.9921875 C-0.18886719 16.00605469 -0.18242187 15.01992188 -0.17578125 14.00390625 C-0.15902344 12.97136719 -0.14226562 11.93882813 -0.125 10.875 C-0.11597656 9.83472656 -0.10695312 8.79445313 -0.09765625 7.72265625 C-0.07403867 5.14834003 -0.03816684 2.57414162 0 0 Z " fill="#0F0F0F" transform="translate(130,47)"/>
<path d="M0 0 C0.85207031 -0.02835938 1.70414063 -0.05671875 2.58203125 -0.0859375 C6.12526124 0.54090079 7.63667198 1.85311168 10.1875 4.375 C12.171875 7.46875 12.171875 7.46875 13.9375 10.875 C14.82695313 12.56109375 14.82695313 12.56109375 15.734375 14.28125 C17.1875 17.375 17.1875 17.375 17.703125 19.59375 C18.06006989 21.48884619 18.06006989 21.48884619 19.64453125 22.83203125 C21.57848232 24.76598232 22.08346584 26.46174491 22.875 29.0625 C23.12636719 29.87074219 23.37773438 30.67898437 23.63671875 31.51171875 C23.81847656 32.12660156 24.00023437 32.74148437 24.1875 33.375 C24.8475 33.375 25.5075 33.375 26.1875 33.375 C26.6825 35.85 26.6825 35.85 27.1875 38.375 C27.8475 38.375 28.5075 38.375 29.1875 38.375 C29.1875 39.035 29.1875 39.695 29.1875 40.375 C29.8475 40.375 30.5075 40.375 31.1875 40.375 C31.1875 41.035 31.1875 41.695 31.1875 42.375 C28.02634224 41.00516497 27.1947908 40.3859362 25.1875 37.375 C20.86861146 34.96150346 17.00186142 35.21180131 12.1875 35.375 C12.1875 31.415 12.1875 27.455 12.1875 23.375 C9.5475 23.375 6.9075 23.375 4.1875 23.375 C4.19910156 24.33277344 4.21070312 25.29054688 4.22265625 26.27734375 C4.30437332 36.34897303 3.88377964 46.32867953 3.1875 56.375 C2.8575 56.375 2.5275 56.375 2.1875 56.375 C1.8575 45.815 1.5275 35.255 1.1875 24.375 C-1.1225 24.045 -3.4325 23.715 -5.8125 23.375 C-5.80089844 24.08527344 -5.78929688 24.79554687 -5.77734375 25.52734375 C-5.76380859 26.90599609 -5.76380859 26.90599609 -5.75 28.3125 C-5.73839844 29.22902344 -5.72679688 30.14554688 -5.71484375 31.08984375 C-5.8125 33.375 -5.8125 33.375 -6.8125 34.375 C-6.96964654 36.00756758 -7.06335414 37.64635338 -7.12890625 39.28515625 C-7.17080078 40.27451172 -7.21269531 41.26386719 -7.25585938 42.28320312 C-7.29517578 43.32412109 -7.33449219 44.36503906 -7.375 45.4375 C-7.41818359 46.48228516 -7.46136719 47.52707031 -7.50585938 48.60351562 C-7.61231225 51.19386884 -7.7143276 53.78432186 -7.8125 56.375 C-8.4725 56.375 -9.1325 56.375 -9.8125 56.375 C-9.8125 45.485 -9.8125 34.595 -9.8125 23.375 C-12.1225 23.375 -14.4325 23.375 -16.8125 23.375 C-16.53428694 17.94984523 -14.25831674 14.69982666 -11.1875 10.42578125 C-9.64912546 8.32763715 -9.64912546 8.32763715 -8.8125 5.375 C-10.7925 5.705 -12.7725 6.035 -14.8125 6.375 C-14.4825 5.715 -14.1525 5.055 -13.8125 4.375 C-11.48239091 3.69715008 -9.14890176 3.03083207 -6.8125 2.375 C-4.33231247 -0.10518753 -3.42734049 0.05138442 0 0 Z " fill="#FACA0C" transform="translate(72.8125,11.625)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 3.96 6 7.92 6 12 C6.66 12 7.32 12 8 12 C8 11.34 8 10.68 8 10 C13.20695956 9.68758243 16.62483347 10.04930629 21 13 C23.45526131 16.90609754 23.74660795 21.50214234 23 26 C20.64067255 29.87603795 19.13008812 30.93495594 15 33 C10.05 32.67 5.1 32.34 0 32 C0 21.44 0 10.88 0 0 Z " fill="#130F06" transform="translate(78,36)"/>
<path d="M0 0 C3.465 1.485 3.465 1.485 7 3 C3.90597382 6.88963291 0.94135793 9.93449939 -3 13 C-3.33 13.66 -3.66 14.32 -4 15 C-3.0409375 15.1546875 -3.0409375 15.1546875 -2.0625 15.3125 C2.06617975 16.23934648 6.00314416 17.62280623 10 19 C10.99 19.33 11.98 19.66 13 20 C12.67 15.38 12.34 10.76 12 6 C13.65 6 15.3 6 17 6 C15.68 6.33 14.36 6.66 13 7 C13.495 11.95 13.495 11.95 14 17 C14.66 17 15.32 17 16 17 C16 15.35 16 13.7 16 12 C16.33 12 16.66 12 17 12 C17 16.29 17 20.58 17 25 C11.06 25 5.12 25 -1 25 C-1 31.6 -1 38.2 -1 45 C-1.66 43.68 -2.32 42.36 -3 41 C-3.66 41 -4.32 41 -5 41 C-6.70680947 39.37446717 -8.37660195 37.70884005 -10 36 C-9.690625 35.401875 -9.38125 34.80375 -9.0625 34.1875 C-7.20244691 30.35797893 -6.8387831 27.34080874 -6.8125 23.125 C-6.79123047 21.58199219 -6.79123047 21.58199219 -6.76953125 20.0078125 C-7.0151262 16.80259022 -7.3765766 14.77827852 -9 12 C-11.59636584 10.32659003 -11.59636584 10.32659003 -14.625 9.1875 C-15.62789063 8.76855469 -16.63078125 8.34960938 -17.6640625 7.91796875 C-18.43492188 7.61503906 -19.20578125 7.31210938 -20 7 C-20 6.67 -20 6.34 -20 6 C-19.31292969 6.02320312 -18.62585938 6.04640625 -17.91796875 6.0703125 C-17.01691406 6.08835937 -16.11585937 6.10640625 -15.1875 6.125 C-14.29417969 6.14820312 -13.40085937 6.17140625 -12.48046875 6.1953125 C-9.74597286 6.18285827 -9.74597286 6.18285827 -7 4 C-5.96875 3.67 -4.9375 3.34 -3.875 3 C-0.87488938 2.3313327 -0.87488938 2.3313327 0 0 Z " fill="#EFBC99" transform="translate(102,63)"/>
<path d="M0 0 C4.62 0 9.24 0 14 0 C14 1.65 14 3.3 14 5 C13 6 13 6 9.9375 6.0625 C8.968125 6.041875 7.99875 6.02125 7 6 C7.02320313 7.05960938 7.04640625 8.11921875 7.0703125 9.2109375 C7.08906944 10.5989513 7.10726972 11.9869727 7.125 13.375 C7.14175781 14.07367188 7.15851563 14.77234375 7.17578125 15.4921875 C7.19344267 17.32897486 7.10303261 19.16601963 7 21 C6.34 21.66 5.68 22.32 5 23 C9.9882251 24.61988753 14.20019296 25.27190003 19.5 25.25 C21.37171875 25.25773437 21.37171875 25.25773437 23.28125 25.265625 C27.19704225 24.98592555 30.2852333 24.20634176 34 23 C34 22.34 34 21.68 34 21 C35.65 21 37.3 21 39 21 C38.01 21.495 38.01 21.495 37 22 C36.34786708 24.02463255 36.34786708 24.02463255 36 26 C36.598125 26.2475 37.19625 26.495 37.8125 26.75 C40.41320743 28.23611853 41.54416577 29.37949839 43 32 C43.3125 35.125 43.3125 35.125 43 38 C41 40 41 40 38 40.1875 C35 40 35 40 33 39 C32.5859375 36.93359375 32.5859375 36.93359375 32.375 34.4375 C32.30023438 33.61121094 32.22546875 32.78492188 32.1484375 31.93359375 C32.09945312 31.29550781 32.05046875 30.65742187 32 30 C30.69804687 30.02320312 29.39609375 30.04640625 28.0546875 30.0703125 C26.32813076 30.08907942 24.60156779 30.10727863 22.875 30.125 C22.0190625 30.14175781 21.163125 30.15851563 20.28125 30.17578125 C15.43412707 30.21306681 11.55292095 29.89580643 7 28 C7 27.34 7 26.68 7 26 C5.824375 25.566875 5.824375 25.566875 4.625 25.125 C2 24 2 24 0 22 C-0.22705078 19.75341797 -0.22705078 19.75341797 -0.1953125 16.9921875 C-0.18886719 16.00605469 -0.18242187 15.01992188 -0.17578125 14.00390625 C-0.15902344 12.97136719 -0.14226562 11.93882813 -0.125 10.875 C-0.11597656 9.83472656 -0.10695312 8.79445313 -0.09765625 7.72265625 C-0.07403867 5.14834003 -0.03816684 2.57414162 0 0 Z " fill="#BB585A" transform="translate(130,47)"/>
<path d="M0 0 C4.23737569 0.04842715 6.95405439 0.36604759 10.25 3.25 C13.04947856 6.98263809 12.25 9.06792449 12.25 14.25 C7.63 14.25 3.01 14.25 -1.75 14.25 C-1.42 14.91 -1.09 15.57 -0.75 16.25 C-0.12351563 16.22679687 0.50296875 16.20359375 1.1484375 16.1796875 C1.96570313 16.16164063 2.78296875 16.14359375 3.625 16.125 C4.43710938 16.10179687 5.24921875 16.07859375 6.0859375 16.0546875 C8.25 16.25 8.25 16.25 10.25 18.25 C10.25 19.24 10.25 20.23 10.25 21.25 C5.91651263 23.71645199 2.13910168 23.81748502 -2.75 23.25 C-6.65059174 21.59017373 -8.1557153 20.10691582 -9.75 16.25 C-10.46332163 11.68805934 -10.41475047 7.6675698 -7.8125 3.75 C-4.93036499 0.25650302 -4.50117443 0.0799025 0 0 Z " fill="#1B1B1B" transform="translate(198.75,45.75)"/>
<path d="M0 0 C-0.65080677 3.41085474 -1.9850826 5.87475305 -3.8125 8.8125 C-4.35132813 9.69035156 -4.89015625 10.56820313 -5.4453125 11.47265625 C-5.95835938 12.30667969 -6.47140625 13.14070313 -7 14 C-8.93986047 17.28234668 -10.7941909 20.58838181 -12.5 24 C-14.2085227 27.4170454 -16.08344571 30.69559606 -18 34 C-18.66 34 -19.32 34 -20 34 C-20.16435547 35.06154297 -20.16435547 35.06154297 -20.33203125 36.14453125 C-22.22320176 44.2290087 -27.31247509 51.6125574 -31.62109375 58.62109375 C-33.74040516 62.27735621 -35.34508253 66.1184663 -37 70 C-39.45287609 75.11904576 -42.21924117 80.05398628 -45 85 C-46 83 -46 83 -45.44140625 81.0546875 C-45.00634766 79.97574219 -45.00634766 79.97574219 -44.5625 78.875 C-44.13130859 77.78832031 -44.13130859 77.78832031 -43.69140625 76.6796875 C-43.46324219 76.12539062 -43.23507813 75.57109375 -43 75 C-42.34 75 -41.68 75 -41 75 C-40.89945313 73.82824219 -40.89945313 73.82824219 -40.796875 72.6328125 C-39.70296702 67.64587905 -37.51156081 63.43022068 -35.25 58.875 C-31.57680045 51.33558454 -28.07142191 43.8078778 -25 36 C-24.34 36 -23.68 36 -23 36 C-22.773125 34.88625 -22.54625 33.7725 -22.3125 32.625 C-21.24173515 28.31931881 -19.42028354 24.70263348 -17.359375 20.79296875 C-15.32713454 16.61753217 -13.83429705 12.26459015 -12.30859375 7.8828125 C-10.91900048 4.82155926 -9.58030578 3.12961897 -7 1 C-4 0 -4 0 0 0 Z " fill="#DF2326" transform="translate(64,17)"/>
<path d="M0 0 C5.28 0 10.56 0 16 0 C16.875 7.6875 16.875 7.6875 17.13623047 10.08081055 C17.60696527 14.01637241 18.29629719 17.3706835 20 21 C21.58226965 21.19627391 23.16589271 21.38165538 24.75 21.5625 C25.63171875 21.66691406 26.5134375 21.77132812 27.421875 21.87890625 C29.88432451 21.99456676 31.66538064 21.73638695 34 21 C34.66 21.99 35.32 22.98 36 24 C27.71481606 28.05804928 18.0187698 28.42391168 9.09765625 26.10546875 C6 25 6 25 5 23 C5.66 22.67 6.32 22.34 7 22 C7.31241371 19.45058627 7.51303421 16.99638487 7.625 14.4375 C7.66367188 13.72658203 7.70234375 13.01566406 7.7421875 12.28320312 C7.83671783 10.52257575 7.91945817 8.76132272 8 7 C9.98 6.67 11.96 6.34 14 6 C14.33 4.35 14.66 2.7 15 1 C13.98679688 1.12568359 13.98679688 1.12568359 12.953125 1.25390625 C12.06109375 1.35574219 11.1690625 1.45757812 10.25 1.5625 C9.36828125 1.66691406 8.4865625 1.77132812 7.578125 1.87890625 C5 2 5 2 1 1 C1 7.93 1 14.86 1 22 C0.67 22 0.34 22 0 22 C0 14.74 0 7.48 0 0 Z " fill="#E5E1E1" transform="translate(129,46)"/>
<path d="M0 0 C2.9719385 1.12451727 5.33440687 2.22293791 8 4 C9.88033997 4.59321869 11.77870208 5.13095819 13.6875 5.625 C14.68136719 5.88539062 15.67523438 6.14578125 16.69921875 6.4140625 C17.45847656 6.60742188 18.21773437 6.80078125 19 7 C19 8.32 19 9.64 19 11 C12.66606918 11.34548714 7.89738492 10.29342747 2 8 C0.50859979 11.62197194 -0.7874262 15.27566618 -2 19 C-3.32 18.67 -4.64 18.34 -6 18 C-6.12375 17.13375 -6.12375 17.13375 -6.25 16.25 C-7.25983994 13.22048017 -8.42149026 11.88161522 -11 10 C-13.81237891 10.20309162 -13.81237891 10.20309162 -17 11 C-21.20182956 11.35076142 -24.75570906 11.63731975 -28.5625 9.6875 C-30 8 -30 8 -30 5 C-20.1 5 -10.2 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#FDF9E7" transform="translate(182,110)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 2.31 6 4.62 6 7 C7.32 7 8.64 7 10 7 C10 8.65 10 10.3 10 12 C8.68 12 7.36 12 6 12 C6 15.3 6 18.6 6 22 C7.32 22 8.64 22 10 22 C10.33 23.98 10.66 25.96 11 28 C4.69635151 29.3334641 4.69635151 29.3334641 1.78125 27.9375 C-0.80779934 25.12134106 -0.39856598 23.06379098 -0.25 19.3125 C-0.21390625 18.13300781 -0.1778125 16.95351563 -0.140625 15.73828125 C-0.09421875 14.83464844 -0.0478125 13.93101563 0 13 C-0.99 13 -1.98 13 -3 13 C-3 11.02 -3 9.04 -3 7 C-2.01 7 -1.02 7 0 7 C0 4.69 0 2.38 0 0 Z " fill="#0D0D0D" transform="translate(176,40)"/>
<path d="M0 0 C2.31 0 4.62 0 7 0 C7 10.56 7 21.12 7 32 C4.69 32 2.38 32 0 32 C0 21.44 0 10.88 0 0 Z " fill="#181308" transform="translate(55,36)"/>
<path d="M0 0 C8.875 1.875 8.875 1.875 10 3 C11.67884238 3.3931648 13.36850045 3.74085452 15.0625 4.0625 C15.98160156 4.23910156 16.90070313 4.41570312 17.84765625 4.59765625 C18.55792969 4.73042969 19.26820312 4.86320312 20 5 C20.33 3.68 20.66 2.36 21 1 C22 2 22 2 22 8 C16.06 8 10.12 8 4 8 C4 14.6 4 21.2 4 28 C3.34 26.68 2.68 25.36 2 24 C1.34 24 0.68 24 0 24 C-1.70680947 22.37446717 -3.37660195 20.70884005 -5 19 C-4.690625 18.401875 -4.38125 17.80375 -4.0625 17.1875 C-2.89790296 14.91043228 -2.89790296 14.91043228 -2 12 C-1.34 12 -0.68 12 0 12 C0 8.04 0 4.08 0 0 Z " fill="#F8BB0F" transform="translate(97,80)"/>
<path d="M0 0 C4.50419983 0.85409827 7.03017333 3.47747469 10.25 6.5625 C10.80429688 7.08263672 11.35859375 7.60277344 11.9296875 8.13867188 C13.29256828 9.41930985 14.64711683 10.7088049 16 12 C13.39710407 15.9043439 11.52955098 15.98430051 7.0625 17.1875 C5.81597656 17.53167969 4.56945312 17.87585938 3.28515625 18.23046875 C0 19 0 19 -3 19 C-3.45510807 12.32508169 -1.7672426 6.40625443 0 0 Z " fill="#FBCE15" transform="translate(71,103)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 19.8 1 39.6 1 60 C1.99 59.67 2.98 59.34 4 59 C5.64279217 58.88207001 7.29052694 58.82488326 8.9375 58.8125 C10.21947266 58.79123047 10.21947266 58.79123047 11.52734375 58.76953125 C14.13909303 59.01296444 15.75500652 59.6821732 18 61 C18 61.33 18 61.66 18 62 C-1.14 62 -20.28 62 -40 62 C-40 61.34 -40 60.68 -40 60 C-40.66 59.67 -41.32 59.34 -42 59 C-28.14 59.33 -14.28 59.66 0 60 C0 40.2 0 20.4 0 0 Z " fill="#F6CF25" transform="translate(53,70)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5.01458252 0.70084717 5.02916504 1.40169434 5.04418945 2.1237793 C5.11885487 5.31223009 5.21545475 8.49965513 5.3125 11.6875 C5.33505859 12.79029297 5.35761719 13.89308594 5.38085938 15.02929688 C5.41630859 16.09599609 5.45175781 17.16269531 5.48828125 18.26171875 C5.51446533 19.24100342 5.54064941 20.22028809 5.56762695 21.22924805 C6.07312992 24.46863439 7.07363808 26.36286423 9 29 C9.99 29.66 10.98 30.32 12 31 C8.04 31 4.08 31 0 31 C0 20.77 0 10.54 0 0 Z " fill="#F8B814" transform="translate(114,101)"/>
<path d="M0 0 C0 0.99 0 1.98 0 3 C0.66 3 1.32 3 2 3 C2.95261487 7.76307437 3.08333357 12.1666528 3 17 C-3.15568076 16.06898067 -8.60845743 15.20578207 -14 12 C-12.48238697 8.17147621 -10.20656585 6.26678134 -7 3.75 C-6.113125 3.04359375 -5.22625 2.3371875 -4.3125 1.609375 C-2 0 -2 0 0 0 Z " fill="#E22428" transform="translate(112,66)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C4.67 1.98 4.34 3.96 4 6 C3.34 6 2.68 6 2 6 C1.7525 6.86625 1.505 7.7325 1.25 8.625 C-0.06778613 12.18302254 -1.80186822 14.9226155 -4 18 C-7.78918443 14.42734039 -11.55592932 10.64893444 -14 6 C-14 4.68 -14 3.36 -14 2 C-10.625 1.875 -10.625 1.875 -7 2 C-6.34 2.66 -5.68 3.32 -5 4 C-4.01 4 -3.02 4 -2 4 C-1.34 2.68 -0.68 1.36 0 0 Z " fill="#FACE18" transform="translate(80,81)"/>
<path d="M0 0 C4.62 0 9.24 0 14 0 C14 1.65 14 3.3 14 5 C7.25 7 7.25 7 5 7 C5 11.62 5 16.24 5 21 C3.35 21 1.7 21 0 21 C0 14.07 0 7.14 0 0 Z " fill="#0F0E0E" transform="translate(130,47)"/>
<path d="M0 0 C1.953125 0.7109375 1.953125 0.7109375 4 2 C4.609375 4.1640625 4.609375 4.1640625 4.75 6.625 C4.80671875 7.44226562 4.8634375 8.25953125 4.921875 9.1015625 C4.94765625 9.72804688 4.9734375 10.35453125 5 11 C7.93985366 12.00660098 7.93985366 12.00660098 10.75 11.0625 C14.1729542 9.44610496 17.23922443 9.87464081 21 10 C21.66 11.65 22.32 13.3 23 15 C19.36576791 17.32590854 16.22911669 17.16265833 12 17 C12 16.01 12 15.02 12 14 C11.01 14.33 10.02 14.66 9 15 C7.31422289 14.38325228 5.64551962 13.71727778 4 13 C3.34 13 2.68 13 2 13 C1.34 13.66 0.68 14.32 0 15 C-0.99 14.67 -1.98 14.34 -3 14 C-2.50886719 13.44828125 -2.01773438 12.8965625 -1.51171875 12.328125 C0.30109424 9.53629931 0.3172951 8.03814259 0.1875 4.75 C0.16042969 3.85796875 0.13335937 2.9659375 0.10546875 2.046875 C0.07066406 1.37140625 0.03585938 0.6959375 0 0 Z " fill="#DBB619" transform="translate(100,52)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C6 6.93 6 13.86 6 21 C4.02 21 2.04 21 0 21 C0 14.07 0 7.14 0 0 Z " fill="#0F0B04" transform="translate(67,47)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 4.29 1 8.58 1 13 C-7.34332544 14.19190363 -15.59323022 14.09283549 -24 14 C-22.9765625 12.0390625 -22.9765625 12.0390625 -21 10 C-18.3427148 9.59524795 -16.03329727 9.3949872 -13.375 9.375 C-10.89310635 9.32886815 -8.47810838 9.20700189 -6 9 C-5.67 8.34 -5.34 7.68 -5 7 C-4.34 7 -3.68 7 -3 7 C-2.690625 6.21625 -2.38125 5.4325 -2.0625 4.625 C-1 2 -1 2 0 0 Z " fill="#FAD12D" transform="translate(100,119)"/>
<path d="M0 0 C1.32 1.32 2.64 2.64 4 4 C1.66666667 6.33333333 -0.66666667 8.66666667 -3 11 C-3.79470703 11.85658203 -3.79470703 11.85658203 -4.60546875 12.73046875 C-6 14 -6 14 -8 14 C-8.43957031 13.55011719 -8.87914063 13.10023437 -9.33203125 12.63671875 C-11.30357075 10.70211676 -13.14942285 9.87922808 -15.6875 8.8125 C-16.49574219 8.46832031 -17.30398437 8.12414062 -18.13671875 7.76953125 C-18.75160156 7.51558594 -19.36648437 7.26164062 -20 7 C-20 6.67 -20 6.34 -20 6 C-19.31292969 6.02320312 -18.62585938 6.04640625 -17.91796875 6.0703125 C-17.01691406 6.08835937 -16.11585937 6.10640625 -15.1875 6.125 C-14.29417969 6.14820312 -13.40085937 6.17140625 -12.48046875 6.1953125 C-9.74597286 6.18285827 -9.74597286 6.18285827 -7 4 C-5.96875 3.67 -4.9375 3.34 -3.875 3 C-0.87488938 2.3313327 -0.87488938 2.3313327 0 0 Z " fill="#F6CA16" transform="translate(102,63)"/>
<path d="M0 0 C9.9 0 19.8 0 30 0 C30 0.33 30 0.66 30 1 C26.04 1 22.08 1 18 1 C18.33 1.99 18.66 2.98 19 4 C13.98164602 6.05881189 9.35985333 6.67125298 4 6 C1.4375 4.6875 1.4375 4.6875 0 3 C0 2.01 0 1.02 0 0 Z " fill="#FDF8E2" transform="translate(152,115)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C0.67 2.66 0.34 3.32 0 4 C0.66 4.33 1.32 4.66 2 5 C1.54109375 5.76183594 1.0821875 6.52367188 0.609375 7.30859375 C-3.01848726 13.41346281 -6.23235673 19.46783722 -9 26 C-11.45287609 31.11904576 -14.21924117 36.05398628 -17 41 C-18 39 -18 39 -17.44140625 37.0546875 C-17.15136719 36.33539063 -16.86132812 35.61609375 -16.5625 34.875 C-16.27503906 34.15054688 -15.98757812 33.42609375 -15.69140625 32.6796875 C-15.46324219 32.12539062 -15.23507813 31.57109375 -15 31 C-14.34 31 -13.68 31 -13 31 C-12.93683594 30.22914062 -12.87367187 29.45828125 -12.80859375 28.6640625 C-11.67084071 23.50844726 -9.38619349 19.0940858 -7.0625 14.375 C-6.37897461 12.96347656 -6.37897461 12.96347656 -5.68164062 11.5234375 C-3.81232915 7.66986421 -1.91982011 3.82865413 0 0 Z " fill="#DF8E66" transform="translate(36,61)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C1.99 4.67 2.98 4.34 4 4 C4 8.29 4 12.58 4 17 C3.34 17 2.68 17 2 17 C1.67 18.32 1.34 19.64 1 21 C0.67 19.02 0.34 17.04 0 15 C-1.32 15 -2.64 15 -4 15 C-4 11.7 -4 8.4 -4 5 C-2.68 5 -1.36 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#ACACAC" transform="translate(186,47)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C1.66 6 2.32 6 3 6 C3 11.94 3 17.88 3 24 C2.67 23.34 2.34 22.68 2 22 C1.34 22 0.68 22 0 22 C-1.70680947 20.37446717 -3.37660195 18.70884005 -5 17 C-4.690625 16.401875 -4.38125 15.80375 -4.0625 15.1875 C-2.89790296 12.91043228 -2.89790296 12.91043228 -2 10 C-1.34 10 -0.68 10 0 10 C0 6.7 0 3.4 0 0 Z " fill="#FCD11E" transform="translate(97,82)"/>
<path d="M0 0 C1.29550781 -0.02126953 1.29550781 -0.02126953 2.6171875 -0.04296875 C5.2491288 0.19890748 6.86480544 0.85766459 9.125 2.1875 C9.125 2.5175 9.125 2.8475 9.125 3.1875 C-10.015 3.1875 -29.155 3.1875 -48.875 3.1875 C-48.875 2.8575 -48.875 2.5275 -48.875 2.1875 C-47.61316895 2.16244385 -46.35133789 2.1373877 -45.05126953 2.11157227 C-40.38091431 2.01757894 -35.71071063 1.91733869 -31.04052734 1.81518555 C-29.01712584 1.77180605 -26.99368701 1.73013322 -24.97021484 1.69018555 C-22.06596616 1.63255592 -19.16190589 1.5688406 -16.2578125 1.50390625 C-15.34943634 1.48719376 -14.44106018 1.47048126 -13.50515747 1.45326233 C-12.66443695 1.43331711 -11.82371643 1.41337189 -10.95751953 1.39282227 C-9.84499313 1.36950584 -9.84499313 1.36950584 -8.70999146 1.34571838 C-5.63139808 1.08027297 -3.26392407 0.02421309 0 0 Z " fill="#F5DA65" transform="translate(61.875,128.8125)"/>
<path d="M0 0 C4.875 1.75 4.875 1.75 6 4 C6.1875 7 6.1875 7 6 10 C4 12 4 12 0.9375 12.1875 C-2 12 -2 12 -3 11 C-3.14115161 8.32941149 -3.04247107 5.67567762 -3 3 C-2.01 3.495 -2.01 3.495 -1 4 C-0.67 2.68 -0.34 1.36 0 0 Z " fill="#E0393C" transform="translate(167,75)"/>
<path d="M0 0 C0.02187378 0.93066284 0.02187378 0.93066284 0.04418945 1.88012695 C0.11866588 4.69154633 0.21532564 7.50177749 0.3125 10.3125 C0.33505859 11.28896484 0.35761719 12.26542969 0.38085938 13.27148438 C0.41630859 14.20927734 0.45175781 15.14707031 0.48828125 16.11328125 C0.51446533 16.97735596 0.54064941 17.84143066 0.56762695 18.73168945 C1.08440552 21.44280729 2.06079478 22.13400933 4 24 C3.6875 27.1875 3.6875 27.1875 3 30 C0.36497961 27.70213773 -0.89701039 25.83077266 -1.33007812 22.33740234 C-1.39355367 19.11294707 -1.3479692 15.91106364 -1.25 12.6875 C-1.23195313 11.56537109 -1.21390625 10.44324219 -1.1953125 9.28710938 C-1.14827856 6.52386553 -1.08256317 3.76237302 -1 1 C-2.65 1 -4.3 1 -6 1 C-6 10.9 -6 20.8 -6 31 C-6.33 31 -6.66 31 -7 31 C-7 20.77 -7 10.54 -7 0 C-4.3333581 -1.33332095 -2.83319697 -0.67102033 0 0 Z " fill="#DB3714" transform="translate(120,100)"/>
<path d="M0 0 C2.5 1 2.5 1 4 2 C4 3.32 4 4.64 4 6 C-1.28 6 -6.56 6 -12 6 C-9.2640762 -0.83980951 -6.88258447 -0.58488366 0 0 Z " fill="#FEFBEE" transform="translate(163,100)"/>
<path d="M0 0 C1.34882814 2.69765628 0.80338265 4.15164334 0 7 C-0.99 7.495 -0.99 7.495 -2 8 C-2.50780287 10.24632473 -2.50780287 10.24632473 -2.8125 12.9375 C-3.77875085 18.80402301 -5.58963702 24.08179107 -9 29 C-9.99 29.33 -10.98 29.66 -12 30 C-11.68950274 28.56206608 -11.3765224 27.1246682 -11.0625 25.6875 C-10.88847656 24.88699219 -10.71445312 24.08648437 -10.53515625 23.26171875 C-10 21 -10 21 -9 18 C-8.34 18 -7.68 18 -7 18 C-6.773125 16.88625 -6.54625 15.7725 -6.3125 14.625 C-5.01939103 9.35817661 -2.55263105 4.7545189 0 0 Z " fill="#DB5151" transform="translate(48,35)"/>
<path d="M0 0 C3.4375 0.1875 3.4375 0.1875 4.4375 1.1875 C4.67279412 6.71691176 4.67279412 6.71691176 3.4375 9.1875 C0.7975 9.1875 -1.8425 9.1875 -4.5625 9.1875 C-5.1875 5.8125 -5.1875 5.8125 -5.5625 2.1875 C-3.5625 0.1875 -3.5625 0.1875 0 0 Z " fill="#DEDEDE" transform="translate(157.5625,52.8125)"/>
<path d="M0 0 C3.5 0.125 3.5 0.125 4.5 1.125 C4.6875 4.0625 4.6875 4.0625 4.5 7.125 C2.5 9.125 2.5 9.125 -0.5625 9.3125 C-3.5 9.125 -3.5 9.125 -4.5 8.125 C-4.625 4.625 -4.625 4.625 -4.5 1.125 C-3.5 0.125 -3.5 0.125 0 0 Z " fill="#D6B21D" transform="translate(89.5,52.875)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C1.32 1.67 2.64 1.34 4 1 C4.33 1.99 4.66 2.98 5 4 C2.85220019 6.14779981 -2.42842452 5.40102482 -5.453125 5.51757812 C-9.11943226 5.7065998 -10.87465937 5.91643958 -14 8 C-12.70989008 5.12206249 -11.42638194 3.03816083 -9 1 C-5.8035914 -0.06546953 -3.34252724 -0.07427838 0 0 Z " fill="#F7E491" transform="translate(90,124)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C4.01 0.495 4.01 0.495 3 1 C2.34786708 3.02463255 2.34786708 3.02463255 2 5 C3.32 5.66 4.64 6.32 6 7 C5.01 7.495 5.01 7.495 4 8 C2.86649466 10.01669827 2.86649466 10.01669827 2 12 C1.67 11.67 1.34 11.34 1 11 C0.67 13.31 0.34 15.62 0 18 C-0.33 18 -0.66 18 -1 18 C-1.03738281 17.38511719 -1.07476562 16.77023438 -1.11328125 16.13671875 C-1.17902344 15.32847656 -1.24476563 14.52023438 -1.3125 13.6875 C-1.37050781 12.88699219 -1.42851562 12.08648437 -1.48828125 11.26171875 C-2.0876557 8.61257517 -3.03626811 7.79526159 -5 6 C-5 5.34 -5 4.68 -5 4 C-3.35 3.34 -1.7 2.68 0 2 C0 1.34 0 0.68 0 0 Z " fill="#D44E52" transform="translate(164,68)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C5.67 4.62 5.34 9.24 5 14 C4.01 13.67 3.02 13.34 2 13 C0.01165038 8.60470084 -0.19118486 4.77962147 0 0 Z " fill="#FCF4DF" transform="translate(132,101)"/>
<path d="M0 0 C2.31 0.33 4.62 0.66 7 1 C7 2.98 7 4.96 7 7 C4.6739143 7.38063221 2.33944736 7.71269945 0 8 C-0.33 7.67 -0.66 7.34 -1 7 C-0.71269945 4.66055264 -0.38063221 2.3260857 0 0 Z " fill="#2D2209" transform="translate(67,35)"/>
<path d="M0 0 C-0.33 1.32 -0.66 2.64 -1 4 C-3.64 4.66 -6.28 5.32 -9 6 C-9.18175781 6.78246094 -9.36351562 7.56492187 -9.55078125 8.37109375 C-9.80214844 9.38300781 -10.05351563 10.39492188 -10.3125 11.4375 C-10.55613281 12.44683594 -10.79976562 13.45617187 -11.05078125 14.49609375 C-11.36402344 15.32238281 -11.67726562 16.14867188 -12 17 C-12.99 17.33 -13.98 17.66 -15 18 C-14.22016274 11.95626123 -11.79553341 4.97619604 -7 1 C-4 0 -4 0 0 0 Z " fill="#DC402E" transform="translate(64,17)"/>
<path d="M0 0 C4.30401897 0.44524334 4.77091701 1.34253683 7.625 4.375 C11.26043901 5.65265265 11.26043901 5.65265265 14.625 6.375 C11.8028917 8.29403365 10.49598068 8.36610212 7 8.125 C3.625 7.375 3.625 7.375 1.625 5.375 C2.615 5.045 3.605 4.715 4.625 4.375 C1.325 3.715 -1.975 3.055 -5.375 2.375 C-3.375 0.375 -3.375 0.375 0 0 Z " fill="#F4DD7C" transform="translate(65.375,126.625)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 3.96 1 7.92 1 12 C-1.31 12.33 -3.62 12.66 -6 13 C-5.731875 12.484375 -5.46375 11.96875 -5.1875 11.4375 C-3.67776746 8.33857531 -2.35062368 5.17102951 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#F8BF1E" transform="translate(100,119)"/>
<path d="M0 0 C1.65 0 3.3 0 5 0 C5 3.3 5 6.6 5 10 C2.525 9.01 2.525 9.01 0 8 C0 5.36 0 2.72 0 0 Z " fill="#FAFAFA" transform="translate(183,53)"/>
<path d="M0 0 C9.9 0 19.8 0 30 0 C30 0.33 30 0.66 30 1 C26.04 1 22.08 1 18 1 C18.33 1.99 18.66 2.98 19 4 C17.68 4.33 16.36 4.66 15 5 C15.33 4.01 15.66 3.02 16 2 C10.72 1.67 5.44 1.34 0 1 C0 0.67 0 0.34 0 0 Z " fill="#F9E8A2" transform="translate(152,115)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C-1.70879335 5.35439668 -4.00933268 5.06501451 -7 5 C-7.33 3.35 -7.66 1.7 -8 0 C-4.94736336 -0.98120464 -3.05263664 -0.98120464 0 0 Z " fill="#131313" transform="translate(122,63)"/>
<path d="M0 0 C-1.32 0.33 -2.64 0.66 -4 1 C-3.67 2.65 -3.34 4.3 -3 6 C-1.68 6.33 -0.36 6.66 1 7 C-1.31 7 -3.62 7 -6 7 C-6 6.01 -6 5.02 -6 4 C-6.99 4.33 -7.98 4.66 -9 5 C-11.1875 4.5625 -11.1875 4.5625 -13 4 C-12.67 3.34 -12.34 2.68 -12 2 C-10.125 1.4375 -10.125 1.4375 -8 1 C-5.07175348 0.02391783 -3.04386724 -0.08226668 0 0 Z " fill="#A07C77" transform="translate(118,62)"/>
<path d="M0 0 C6.75 0.75 6.75 0.75 9 3 C0.57142857 5.28571429 0.57142857 5.28571429 -4 5 C-3.67 3.68 -3.34 2.36 -3 1 C-2.01 1.33 -1.02 1.66 0 2 C0 1.34 0 0.68 0 0 Z " fill="#F4EDC5" transform="translate(59,124)"/>
<path d="M0 0 C0.99 0 1.98 0 3 0 C2.52467862 5.07009471 -0.04362294 8.97493258 -3 13 C-3.33 13 -3.66 13 -4 13 C-4 10.36 -4 7.72 -4 5 C-2.68 4.67 -1.36 4.34 0 4 C0 2.68 0 1.36 0 0 Z " fill="#DB321B" transform="translate(49,38)"/>
<path d="M0 0 C2 2 2 2 3.5 5.5625 C4.58995587 9.06202674 4.58995587 9.06202674 7 10 C7.625 12.5625 7.625 12.5625 8 15 C7.34 14.67 6.68 14.34 6 14 C6 13.01 6 12.02 6 11 C5.195625 11.020625 4.39125 11.04125 3.5625 11.0625 C1 11 1 11 0 10 C-0.07205511 8.31391034 -0.08386068 6.62499341 -0.0625 4.9375 C-0.05347656 4.01839844 -0.04445312 3.09929687 -0.03515625 2.15234375 C-0.02355469 1.44207031 -0.01195312 0.73179688 0 0 Z " fill="#F6B41B" transform="translate(132,108)"/>
<path d="M0 0 C0.33 0.99 0.66 1.98 1 3 C-0.134375 2.835 -1.26875 2.67 -2.4375 2.5 C-8.6553978 1.7282751 -14.88560643 1.42209198 -21 3 C-20 1 -20 1 -17 0 C-11.22336757 -0.81616471 -5.73015639 -1.39382183 0 0 Z " fill="#F6D283" transform="translate(170,87)"/>
<path d="M0 0 C0.90492187 0.02320313 1.80984375 0.04640625 2.7421875 0.0703125 C3.65226562 0.08835937 4.56234375 0.10640625 5.5 0.125 C6.19867188 0.14820313 6.89734375 0.17140625 7.6171875 0.1953125 C7.6171875 0.5253125 7.6171875 0.8553125 7.6171875 1.1953125 C4.6471875 1.1953125 1.6771875 1.1953125 -1.3828125 1.1953125 C-1.7128125 2.5153125 -2.0428125 3.8353125 -2.3828125 5.1953125 C-4.4453125 6.4453125 -4.4453125 6.4453125 -6.3828125 7.1953125 C-6.3828125 6.5353125 -6.3828125 5.8753125 -6.3828125 5.1953125 C-8.3628125 5.5253125 -10.3428125 5.8553125 -12.3828125 6.1953125 C-12.0528125 5.5353125 -11.7228125 4.8753125 -11.3828125 4.1953125 C-9.05270341 3.51746258 -6.71921426 2.85114457 -4.3828125 2.1953125 C-2.3828125 0.1953125 -2.3828125 0.1953125 0 0 Z " fill="#F3CB5E" transform="translate(70.3828125,11.8046875)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C2.5625 1.9375 2.5625 1.9375 3 4 C2.67 4.33 2.34 4.66 2 5 C3.32 5.66 4.64 6.32 6 7 C4.68 7 3.36 7 2 7 C2 7.66 2 8.32 2 9 C0.68 8.67 -0.64 8.34 -2 8 C-2 6.35 -2 4.7 -2 3 C-1.34 3 -0.68 3 0 3 C0 2.01 0 1.02 0 0 Z " fill="#F8F5E2" transform="translate(57,117)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C1.95199219 1.93972656 1.95199219 1.93972656 0.8828125 2.8984375 C-0.02726562 3.71570313 -0.93734375 4.53296875 -1.875 5.375 C-2.77992187 6.18710938 -3.68484375 6.99921875 -4.6171875 7.8359375 C-6.10896183 9.19076205 -7.57505435 10.57505435 -9 12 C-9.33 11.01 -9.66 10.02 -10 9 C-6.7 6.03 -3.4 3.06 0 0 Z " fill="#ECAE76" transform="translate(106,66)"/>
<path d="M0 0 C1.32 0.33 2.64 0.66 4 1 C3.67 1.99 3.34 2.98 3 4 C4.32 4.66 5.64 5.32 7 6 C6.01 6.33 5.02 6.66 4 7 C4 6.34 4 5.68 4 5 C2.68 5.33 1.36 5.66 0 6 C0 5.34 0 4.68 0 4 C-0.99 3.67 -1.98 3.34 -3 3 C-2.01 2.67 -1.02 2.34 0 2 C0 1.34 0 0.68 0 0 Z " fill="#FAF3D8" transform="translate(90,120)"/>
<path d="M0 0 C0.99 0.33 1.98 0.66 3 1 C0.69 4.96 -1.62 8.92 -4 13 C-5 10 -5 10 -4 7 C-3.34 7 -2.68 7 -2 7 C-2 5.35 -2 3.7 -2 2 C-1.34 2 -0.68 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#DB3116" transform="translate(59,21)"/>
<path d="M0 0 C0.66 0.99 1.32 1.98 2 3 C1.42578125 5.15625 1.42578125 5.15625 0.3125 7.5 C-0.04199219 8.2734375 -0.39648438 9.046875 -0.76171875 9.84375 C-1.96896881 11.94596461 -3.20604183 13.38698862 -5 15 C-4.54901762 13.06077578 -4.08894441 11.12366406 -3.625 9.1875 C-3.24214844 7.56908203 -3.24214844 7.56908203 -2.8515625 5.91796875 C-2 3 -2 3 0 0 Z " fill="#D77141" transform="translate(31,72)"/>
<path d="M0 0 C4.62 0 9.24 0 14 0 C14 0.33 14 0.66 14 1 C5.57142857 3.28571429 5.57142857 3.28571429 1 3 C0.67 2.01 0.34 1.02 0 0 Z " fill="#BDBDBD" transform="translate(197,60)"/>
<path d="M0 0 C4.95 0 9.9 0 15 0 C10.65352612 3.25985541 10.2519968 3.34252339 5.25 2.6875 C4.26515625 2.56761719 3.2803125 2.44773438 2.265625 2.32421875 C1.51796875 2.21722656 0.7703125 2.11023437 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C6C6C6" transform="translate(111,60)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1.95261487 4.76307437 2.08333357 9.1666528 2 14 C1.01 14 0.02 14 -1 14 C-0.67 9.38 -0.34 4.76 0 0 Z " fill="#DE3D40" transform="translate(113,69)"/>
<path d="M0 0 C0.33 1.32 0.66 2.64 1 4 C-3.455 4.495 -3.455 4.495 -8 5 C-7.67 3.68 -7.34 2.36 -7 1 C-2.25 0 -2.25 0 0 0 Z " fill="#D4B020" transform="translate(74,42)"/>
<path d="M0 0 C0 3.13193134 -0.53360752 5.59730514 -1.3125 8.625 C-1.55613281 9.60726563 -1.79976562 10.58953125 -2.05078125 11.6015625 C-2.36402344 12.39304687 -2.67726562 13.18453125 -3 14 C-3.99 14.33 -4.98 14.66 -6 15 C-5.38602623 10.24170326 -3.4893031 3.4893031 0 0 Z " fill="#DB6668" transform="translate(55,20)"/>
<path d="M0 0 C1.98 0 3.96 0 6 0 C5.67 0.66 5.34 1.32 5 2 C7.97 3.485 7.97 3.485 11 5 C9.375 5.75 9.375 5.75 7 6 C1.4109589 2.82191781 1.4109589 2.82191781 0 0 Z " fill="#C75A5E" transform="translate(130,68)"/>
<path d="M0 0 C0.495 0.37125 0.99 0.7425 1.5 1.125 C1.17 1.785 0.84 2.445 0.5 3.125 C-2.0625 3.75 -2.0625 3.75 -4.5 4.125 C-4.5 3.795 -4.5 3.465 -4.5 3.125 C-7.47 2.63 -7.47 2.63 -10.5 2.125 C-10.5 1.795 -10.5 1.465 -10.5 1.125 C-3.11538462 -1.09038462 -3.11538462 -1.09038462 0 0 Z " fill="#D5C9C9" transform="translate(163.5,67.875)"/>
<path d="M0 0 C0.33 0.66 0.66 1.32 1 2 C1.66 2 2.32 2 3 2 C3 2.66 3 3.32 3 4 C2.34 4 1.68 4 1 4 C1 7.96 1 11.92 1 16 C0.67 16 0.34 16 0 16 C-0.06058594 15.16855469 -0.12117188 14.33710937 -0.18359375 13.48046875 C-0.26738281 12.39378906 -0.35117188 11.30710937 -0.4375 10.1875 C-0.51871094 9.10855469 -0.59992188 8.02960938 -0.68359375 6.91796875 C-0.90212461 3.94010095 -0.90212461 3.94010095 -2 1 C-1.34 0.67 -0.68 0.34 0 0 Z " fill="#F9E08C" transform="translate(96,76)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C-1.72143296 4.91447559 -5.14032976 8.49653351 -10 11 C-8.55218207 7.55571735 -6.72305391 5.63869038 -3.875 3.25 C-3.15054688 2.63640625 -2.42609375 2.0228125 -1.6796875 1.390625 C-0.84824219 0.70226562 -0.84824219 0.70226562 0 0 Z " fill="#DC3432" transform="translate(108,67)"/>
<path d="M0 0 C2.475 0.495 2.475 0.495 5 1 C5 2.32 5 3.64 5 5 C3.35 5 1.7 5 0 5 C0 3.35 0 1.7 0 0 Z " fill="#FACA02" transform="translate(87,55)"/>
<path d="M0 0 C1.98 0.99 1.98 0.99 4 2 C4 3.98 4 5.96 4 8 C3.34 8 2.68 8 2 8 C1.34 8.99 0.68 9.98 0 11 C0 7.37 0 3.74 0 0 Z " fill="#E5BD18" transform="translate(63,35)"/>
<path d="M0 0 C3.09592835 3.5087188 4.74194623 6.49197399 6 11 C4 11 4 11 2.4375 10 C0.19187539 6.87565271 0.31165434 3.80218292 0 0 Z " fill="#F6D649" transform="translate(84,20)"/>
<path d="M0 0 C1.9375 0.375 1.9375 0.375 4 1 C4.33 1.66 4.66 2.32 5 3 C4.01 3.33 3.02 3.66 2 4 C0.78130071 6.50447516 0.78130071 6.50447516 0 9 C-1.10844919 5.67465243 -0.84454356 3.37817425 0 0 Z " fill="#F8CF30" transform="translate(71,103)"/>
<path d="M0 0 C0 0.33 0 0.66 0 1 C-1.0725 1.28875 -2.145 1.5775 -3.25 1.875 C-6.09400364 2.72820109 -8.43508983 3.5636503 -11 5 C-10.625 3.0625 -10.625 3.0625 -10 1 C-6.78548731 -0.60725635 -3.56387464 -0.05748185 0 0 Z " fill="#FCF3CE" transform="translate(163,100)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C1.47731534 2.76276177 0.89130413 5.3260876 0 8 C-0.99 7.67 -1.98 7.34 -3 7 C-2.690625 6.401875 -2.38125 5.80375 -2.0625 5.1875 C-0.89790296 2.91043228 -0.89790296 2.91043228 0 0 Z " fill="#F8D94F" transform="translate(95,92)"/>
<path d="M0 0 C0.99 0.99 1.98 1.98 3 3 C2.67 3.99 2.34 4.98 2 6 C1.67 5.67 1.34 5.34 1 5 C0.67 7.31 0.34 9.62 0 12 C-0.33 12 -0.66 12 -1 12 C-1.02712066 10.56260487 -1.04645067 9.12506137 -1.0625 7.6875 C-1.07410156 6.88699219 -1.08570313 6.08648437 -1.09765625 5.26171875 C-1 3 -1 3 0 0 Z " fill="#E78485" transform="translate(164,74)"/>
<path d="M0 0 C0.66 0 1.32 0 2 0 C6 4.47058824 6 4.47058824 6 7 C4.35 6.67 2.7 6.34 1 6 C0.67 4.02 0.34 2.04 0 0 Z " fill="#F9D236" transform="translate(78,13)"/>
<path d="M0 0 C6.15234375 0.5859375 6.15234375 0.5859375 8 1 C8.33 1.66 8.66 2.32 9 3 C5.7 2.67 2.4 2.34 -1 2 C-0.67 1.34 -0.34 0.68 0 0 Z " fill="#F5CC24" transform="translate(62,129)"/>
<path d="M0 0 C1.98 0.99 1.98 0.99 4 2 C4 4.64 4 7.28 4 10 C1.2027092 7.93243723 0.97307727 6.85559629 0.3125 3.3125 C0.209375 2.219375 0.10625 1.12625 0 0 Z " fill="#AA8E2E" transform="translate(100,52)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.98 1 3.96 1 6 C-2.37462715 5.45276317 -4.08235 4.9451 -7 3 C-4.69 3.33 -2.38 3.66 0 4 C0 2.68 0 1.36 0 0 Z " fill="#FBCA46" transform="translate(117,81)"/>
<path d="M0 0 C0.66 0.33 1.32 0.66 2 1 C0.7260411 3.03833424 -0.61061923 5.03852126 -2 7 C-2.66 7 -3.32 7 -4 7 C-3.6875 4.625 -3.6875 4.625 -3 2 C-2.01 1.34 -1.02 0.68 0 0 Z " fill="#D73B1D" transform="translate(40,58)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-2.31 2 -4.62 2 -7 2 C-7 1.34 -7 0.68 -7 0 C-4.3333581 -1.33332095 -2.83319697 -0.67102033 0 0 Z " fill="#D2D2D2" transform="translate(118,53)"/>
<path d="M0 0 C0.33 0 0.66 0 1 0 C1 1.65 1 3.3 1 5 C-2.3 4.34 -5.6 3.68 -9 3 C-9 2.67 -9 2.34 -9 2 C-6.03 2 -3.06 2 0 2 C0 1.34 0 0.68 0 0 Z " fill="#C4A221" transform="translate(96,43)"/>
<path d="M0 0 C0 0.66 0 1.32 0 2 C-2.31 2 -4.62 2 -7 2 C-7 1.34 -7 0.68 -7 0 C-4.3333581 -1.33332095 -2.83319697 -0.67102033 0 0 Z " fill="#D4D4D4" transform="translate(204,53)"/>
</svg>

                    <!-- <img src="https://tinyurl.com/2yhjshr2" alt="Liberate Bites Logo"> -->
                </a>
                
                <nav role="navigation" aria-label="Main navigation">
                    <ul class="nav-menu">
                        <li><a href="#home" aria-label="Go to home section">Home</a></li>
                        <li><a href="#about" aria-label="Learn about us">About</a></li>
                        <li><a href="#contact" aria-label="Contact us">Contact</a></li>
                    </ul>
                </nav>
                
                <div class="header-actions">
                    <div class="search-box">
                        <label for="search-input" class="sr-only">Search for products</label>
                        <input type="text" id="search-input" placeholder="Search for cakes, breads..." aria-label="Search for products">
                        <button type="submit" aria-label="Search">🔍</button>
                    </div>
                    
                    <div class="user-icon">👤</div>
                    
                    <div class="cart-icon">
                        🛒
                        <span class="cart-count">0</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Banner - Exact Flurys Style -->
    <section id="home" class="hero-banner">
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-title">Freshly Baked Delights</h1>
                <p class="hero-subtitle">Indulge in our handcrafted cakes, artisan breads, and premium pastries delivered fresh to your doorstep. Experience the perfect blend of tradition and taste.</p>
                <div class="hero-buttons">
                    <button class="hero-btn primary" onclick="scrollToSection('featured')">Order Now</button>
                    <button class="hero-btn secondary" onclick="scrollToSection('featured')">View Menu</button>
                </div>
            </div>
            <div class="hero-image">
                <img src="https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=600&h=400&fit=crop" alt="Delicious Cakes" class="hero-img">
            </div>
        </div>
    </section>

    <!-- Below Banner Stats Section - Exact Flurys Style -->
    <section class="stats-section">
        <div class="stats-container">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-icon">😊</div>
                    <div class="stat-number">500+</div>
                    <div class="stat-label">Happy Customers</div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">🎂</div>
                    <div class="stat-number">50+</div>
                    <div class="stat-label">Cake Varieties</div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">🚚</div>
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">Fresh Delivery</div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">⭐</div>
                    <div class="stat-number">4.9</div>
                    <div class="stat-label">Rating</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products Section -->
    <section id="featured" class="featured-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Featured Products</h2>
                <p class="section-subtitle">Discover our most popular handcrafted delights, made with love and the finest ingredients</p>
            </div>
            
            <div class="product-grid">
                <div class="product-card">
                    <img src="https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=300&fit=crop" alt="Chocolate Truffle Cake" class="product-image">
                    <div class="product-info">
                        <h3 class="product-name">Chocolate Truffle Cake</h3>
                        <p class="product-description">Rich chocolate sponge cake layered with creamy chocolate truffle. Perfect for celebrations.</p>
                        <div class="product-price">₹899</div>
                        <button class="buy-button">Add to Cart</button>
                    </div>
                </div>
                
                <div class="product-card">
                    <img src="https://images.unsplash.com/photo-1464349095431-e9a21285b5f3?w=400&h=300&fit=crop" alt="Black Forest Cake" class="product-image">
                    <div class="product-info">
                        <h3 class="product-name">Black Forest Cake</h3>
                        <p class="product-description">German chocolate sponge with truffle and cherry fresh cream layers.</p>
                        <div class="product-price">₹999</div>
                        <button class="buy-button">Add to Cart</button>
                    </div>
                </div>
                
                <div class="product-card">
                    <img src="https://images.unsplash.com/photo-1549007953-2f2dc0b24019?w=400&h=300&fit=crop" alt="Fresh Milk Loaf" class="product-image">
                    <div class="product-info">
                        <h3 class="product-name">Fresh Milk Loaf</h3>
                        <p class="product-description">Soft and fluffy milk bread with rich texture and slightly sweet flavor.</p>
                        <div class="product-price">₹89</div>
                        <button class="buy-button">Add to Cart</button>
                    </div>
                </div>
                
                <div class="product-card">
                    <img src="https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=400&h=300&fit=crop" alt="Pineapple Cake" class="product-image">
                    <div class="product-info">
                        <h3 class="product-name">Pineapple Cake</h3>
                        <p class="product-description">Vanilla sponge with pineapple chunks, fresh cream and white chocolate.</p>
                        <div class="product-price">₹799</div>
                        <button class="buy-button">Add to Cart</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Cakes Section -->
    <section id="cakes" class="featured-section" style="background: #ffffff;">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Our Cakes</h2>
                <p class="section-subtitle">Celebrate life's special moments with our handcrafted cakes made from the finest ingredients</p>
            </div>
            <div class="product-grid">
                <div class="product-card">
                    <img src="https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=300&fit=crop" alt="Chocolate Truffle Cake" class="product-image">
                    <div class="product-info">
                        <h3 class="product-name">Chocolate Truffle Cake</h3>
                        <p class="product-description">Rich chocolate sponge cake layered with creamy chocolate truffle.</p>
                        <div class="product-price">₹899</div>
                        <button class="buy-button">Add to Cart</button>
                    </div>
                </div>
                <div class="product-card">
                    <img src="https://images.unsplash.com/photo-1464349095431-e9a21285b5f3?w=400&h=300&fit=crop" alt="Black Forest Cake" class="product-image">
                    <div class="product-info">
                        <h3 class="product-name">Black Forest Cake</h3>
                        <p class="product-description">German chocolate sponge with truffle and cherry fresh cream layers.</p>
                        <div class="product-price">₹999</div>
                        <button class="buy-button">Add to Cart</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Breads Section -->
    <section id="breads" class="featured-section" style="background: #f8f8f8;">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Fresh Breads</h2>
                <p class="section-subtitle">Daily baked artisan breads with traditional recipes and modern techniques</p>
            </div>
            <div class="product-grid">
                <div class="product-card">
                    <img src="https://images.unsplash.com/photo-1549007953-2f2dc0b24019?w=400&h=300&fit=crop" alt="Fresh Milk Loaf" class="product-image">
                    <div class="product-info">
                        <h3 class="product-name">Fresh Milk Loaf</h3>
                        <p class="product-description">Soft and fluffy milk bread with rich texture and slightly sweet flavor.</p>
                        <div class="product-price">₹89</div>
                        <button class="buy-button">Add to Cart</button>
                    </div>
                </div>
                <div class="product-card">
                    <img src="https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=300&fit=crop" alt="Whole Wheat Bread" class="product-image">
                    <div class="product-info">
                        <h3 class="product-name">Whole Wheat Bread</h3>
                        <p class="product-description">Healthy whole wheat bread packed with nutrients and fiber.</p>
                        <div class="product-price">₹65</div>
                        <button class="buy-button">Add to Cart</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pastries Section -->
    <section id="pastries" class="featured-section" style="background: #ffffff;">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Delicious Pastries</h2>
                <p class="section-subtitle">Indulge in our selection of flaky, buttery pastries perfect for any time of day</p>
            </div>
            <div class="product-grid">
                <div class="product-card">
                    <img src="https://images.unsplash.com/photo-**********-ab794f4afe5e?w=400&h=300&fit=crop" alt="Croissant" class="product-image">
                    <div class="product-info">
                        <h3 class="product-name">Butter Croissant</h3>
                        <p class="product-description">Flaky, buttery croissant made with premium French butter.</p>
                        <div class="product-price">₹45</div>
                        <button class="buy-button">Add to Cart</button>
                    </div>
                </div>
                <div class="product-card">
                    <img src="https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=400&h=300&fit=crop" alt="Danish Pastry" class="product-image">
                    <div class="product-info">
                        <h3 class="product-name">Danish Pastry</h3>
                        <p class="product-description">Sweet Danish pastry with fruit filling and delicate glaze.</p>
                        <div class="product-price">₹55</div>
                        <button class="buy-button">Add to Cart</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="featured-section" style="background: #ffffff;">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">About Liberate Bites</h2>
                <p class="section-subtitle">We are passionate bakers dedicated to creating exceptional treats that bring joy to every occasion. Our commitment to quality ingredients and traditional techniques ensures every bite is a delightful experience.</p>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="featured-section" style="background: #f8f8f8;">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Contact Us</h2>
                <p class="section-subtitle">Get in touch with us for custom orders, catering, or any questions about our delicious baked goods.</p>
            </div>
            <div style="text-align: center; margin-top: 40px;">
                <p style="font-size: 18px; margin-bottom: 10px;"><strong>📞 Phone:</strong> +91 98765 43210</p>
                <p style="font-size: 18px; margin-bottom: 10px;"><strong>📧 Email:</strong> <EMAIL></p>
                <p style="font-size: 18px; margin-bottom: 10px;"><strong>📍 Address:</strong> 123 Baker Street, Cake City, India</p>
                <p style="font-size: 18px;"><strong>🕒 Hours:</strong> Mon-Sun: 8:00 AM - 10:00 PM</p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer style="background: linear-gradient(135deg, #333333 0%, #2a2a2a 100%); color: white; padding: 40px 0; text-align: center; border-top: 3px solid #fa4f1f;">
        <div class="container">
            <p style="margin-bottom: 20px; font-size: 18px; font-weight: 500;">&copy; 2024 Liberate Bites. All rights reserved.</p>
            <p style="font-size: 14px; opacity: 0.8;">Made with <span style="color: #fa4f1f;">❤️</span> for cake lovers everywhere</p>
        </div>
    </footer>

    <script>
        // Simple cart functionality
        let cartCount = 0;
        const cartCountElement = document.querySelector('.cart-count');

        // Function to add cart functionality to buttons
        function addCartFunctionality() {
            const addToCartButtons = document.querySelectorAll('.buy-button');
            addToCartButtons.forEach(button => {
                // Remove existing listeners to avoid duplicates
                button.removeEventListener('click', handleCartClick);
                button.addEventListener('click', handleCartClick);
            });
        }

        function handleCartClick() {
            cartCount++;
            cartCountElement.textContent = cartCount;

            // Visual feedback
            this.style.background = '#28a745';
            this.textContent = 'Added!';

            setTimeout(() => {
                this.style.background = 'linear-gradient(135deg, #fbc900 0%, #fa4f1f 100%)';
                this.style.borderColor = '#fbc900';
                this.textContent = 'Add to Cart';
            }, 1000);
        }

        // Initialize cart functionality
        addCartFunctionality();

        // Enhanced search functionality
        const searchInput = document.querySelector('.search-box input');
        const searchButton = document.querySelector('.search-box button');

        function performSearch() {
            const searchTerm = searchInput.value.trim().toLowerCase();
            if (searchTerm) {
                const productCards = document.querySelectorAll('.product-card');
                let foundResults = false;

                productCards.forEach(card => {
                    const productName = card.querySelector('.product-name').textContent.toLowerCase();
                    const productDescription = card.querySelector('.product-description').textContent.toLowerCase();

                    if (productName.includes(searchTerm) || productDescription.includes(searchTerm)) {
                        card.style.display = 'block';
                        card.style.border = '2px solid #fa4f1f';
                        foundResults = true;
                    } else {
                        card.style.display = 'none';
                        card.style.border = 'none';
                    }
                });

                if (!foundResults) {
                    alert(`No products found for: "${searchTerm}". Try searching for "cake", "bread", or "pastry".`);
                    // Reset all cards to visible
                    productCards.forEach(card => {
                        card.style.display = 'block';
                        card.style.border = 'none';
                    });
                } else {
                    // Scroll to featured section to show results
                    scrollToSection('featured');
                }
            } else {
                // Reset search - show all products
                const productCards = document.querySelectorAll('.product-card');
                productCards.forEach(card => {
                    card.style.display = 'block';
                    card.style.border = 'none';
                });
            }
        }

        searchButton.addEventListener('click', performSearch);

        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Clear search when input is empty
        searchInput.addEventListener('input', function() {
            if (this.value.trim() === '') {
                const productCards = document.querySelectorAll('.product-card');
                productCards.forEach(card => {
                    card.style.display = 'block';
                    card.style.border = 'none';
                });
            }
        });

        // Smooth scrolling function
        function scrollToSection(sectionId) {
            const target = document.getElementById(sectionId);
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add loading animation
        window.addEventListener('load', function() {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease-in-out';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });

        // Add scroll-to-top functionality
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            if (scrollTop > 300) {
                if (!document.querySelector('.scroll-to-top')) {
                    const scrollBtn = document.createElement('button');
                    scrollBtn.className = 'scroll-to-top';
                    scrollBtn.innerHTML = '↑';
                    scrollBtn.style.cssText = `
                        position: fixed;
                        bottom: 20px;
                        right: 20px;
                        background: linear-gradient(135deg, #fbc900 0%, #fa4f1f 100%);
                        color: black;
                        border: none;
                        border-radius: 50%;
                        width: 50px;
                        height: 50px;
                        font-size: 20px;
                        cursor: pointer;
                        z-index: 1000;
                        transition: all 0.3s ease;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                    `;
                    scrollBtn.addEventListener('click', () => {
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                    });
                    document.body.appendChild(scrollBtn);
                }
            } else {
                const scrollBtn = document.querySelector('.scroll-to-top');
                if (scrollBtn) {
                    scrollBtn.remove();
                }
            }
        });
    </script>
</body>
</html>