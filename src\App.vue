
<template>
  <div>
    <nav>
      <router-link to="/moods">Moods</router-link> |
      <router-link to="/books">Books</router-link> |
      <router-link to="/food-items">Foods</router-link> |
      <router-link to="/login" @click="logout">Logout</router-link>
    </nav>
    <router-view />
  </div>
</template>

<script setup>
const logout = () => {
  localStorage.removeItem('token')
}
</script>

<style scoped>
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
