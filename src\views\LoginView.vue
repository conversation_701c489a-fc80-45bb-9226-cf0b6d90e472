<template>
  <div>
    <h2>Admin <PERSON>gin</h2>
    <form @submit.prevent="login">
      <input v-model="email" type="email" placeholder="Email" />
      <input v-model="password" type="password" placeholder="Password" />
      <button type="submit">Login</button>
    </form>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import axios from '../api/axios'
import { useRouter } from 'vue-router'

const email = ref('')
const password = ref('')
const router = useRouter()

const login = async () => {
  try {
    const res = await axios.post('admin/login', {
      email: email.value,
      password: password.value
    })
    localStorage.setItem('token', res.data.token)
    alert('Login successful')
    router.push('/moods')
  } catch (err) {
    alert('Login failed: ' + err.response?.data?.message)
  }
}
</script>
